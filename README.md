# PixivTagDownloader

A powerful C++ application for downloading Pixiv artworks by user ID and tags.

## Features

- **Cookie-based Authentication**: Secure login using your Pixiv session cookie
- **Flexible Tag Filtering**: Support for AND/OR/NOT logic with multiple tags
- **Multiple Artwork Types**: Download illustrations, manga, and novels
- **Configurable Organization**: Highly customizable directory structure and file naming
- **Concurrent Downloads**: Multi-threaded downloading with configurable rate limiting
- **Multiple Download Methods**: Direct download, aria2c command-line, or aria2 RPC
- **Progress Tracking**: Real-time progress display and comprehensive logging
- **Cross-platform**: Supports Windows, Linux, and macOS
- **Interactive & CLI Modes**: Both interactive interface and command-line operation

## Quick Start

### Prerequisites

- C++17 compatible compiler (GCC ≥ 9, Clang ≥ 10, MSVC ≥ 2019)
- CMake ≥ 3.15
- Internet connection to download dependencies

### Building

```bash
# Clone the repository
git clone https://github.com/your-username/PixivTagDownloader-CPP.git
cd PixivTagDownloader-CPP

# Create build directory
mkdir build && cd build

# Configure and build
cmake ..
make -j$(nproc)

# Or on Windows with Visual Studio
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
```

### Configuration

1. Copy the example configuration file:
   ```bash
   cp config.yaml.example config.yaml
   ```

2. Edit `config.yaml` and set your Pixiv session cookie:
   - Login to Pixiv in your browser
   - Open Developer Tools (F12)
   - Go to Application/Storage → Cookies → https://www.pixiv.net
   - Copy the entire cookie string and paste it in the config file

### Usage

#### Interactive Mode
```bash
./PixivTagDownloader
```
Follow the interactive prompts to specify user ID, tags, and download options.

#### Command Line Mode
```bash
# Download all artworks from user 123456
./PixivTagDownloader -u 123456 --all

# Download artworks with specific tags
./PixivTagDownloader -u 123456 -t "original,illustration" -l and

# Download only illustrations and manga
./PixivTagDownloader -u 123456 -T "Illust,Manga"

# Use custom output directory
./PixivTagDownloader -u 123456 --output-dir "/path/to/downloads"
```

## Configuration Options

The `config.yaml` file supports extensive customization:

### Authentication
```yaml
cookie: "your_pixiv_session_cookie_here"
```

### Path Templates
```yaml
path_template:
  directory: "{uid}_{username}/{type}/{series_title}"
  filename: "{upload_date}_{pid}_{title}"
  page_index_format: "p{:02d}"
```

Available template variables:
- `{uid}`, `{username}` - User information
- `{pid}`, `{title}`, `{type}` - Artwork information  
- `{series_title}`, `{series_id}` - Series information
- `{upload_date}` - Upload date (YYYYMMDD format)
- `{tags}` - Artwork tags
- `{page_index}`, `{page_count}` - Page information
- `{r18}` - R18 flag
- `{like_count}`, `{bookmark_count}` - Statistics
- `{ext}` - File extension

### Download Settings
```yaml
download:
  method: "direct"  # direct, aria2c, aria2rpc
  concurrency: 4
  delay_range: "1-3"
  conflict_strategy: "skip"  # skip, overwrite, rename
```

### Aria2 Integration
```yaml
download:
  method: "aria2rpc"
  aria2:
    rpc_url: "ws://localhost:6800/jsonrpc"
    secret: "your_rpc_secret"
    verify_ssl: true
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `-u, --uid` | Target Pixiv user ID | Required |
| `-t, --tags` | Tags to filter by (comma-separated) | None |
| `-l, --logic` | Tag filtering logic (and/or/not) | or |
| `--all` | Download all artworks | false |
| `-T, --type` | Artwork types (Illust,Manga,Novel,all) | all |
| `--output-dir` | Output directory | Output |
| `--download-method` | Download method | direct |
| `--threads` | Concurrent downloads | CPU cores |
| `--delay` | Random delay range (seconds) | 1-3 |
| `--log-level` | Logging level | info |

## File Organization

Downloaded files are organized according to your path templates:

```
Output/
├── 123456_ArtistName/
│   ├── Illust/
│   │   ├── SeriesName/
│   │   │   ├── 20240101_98765432_ArtworkTitle.jpg
│   │   │   ├── 20240101_98765432_ArtworkTitle.txt
│   │   │   └── ...
│   │   └── No_Series/
│   ├── Manga/
│   └── Novel/
└── ...
```

### Metadata Files

Each artwork includes a metadata file with complete information:

```
Title: Artwork Title
Author_UID: 123456
Author_Username: ArtistName
Artwork_PID: 98765432
Artwork_Type: Illust
Tags: [tag1, tag2, tag3]
Description:
Artwork description here...
Upload_Date: 2024-01-01T12:00:00Z
Page_Count: 1
R18: False
Like_Count: 1234
Bookmark_Count: 567
Original_URLs:
https://i.pximg.net/img-original/...
Download_Time: 2024-01-01T15:30:00Z
```

## Dependencies

The project automatically downloads and builds the following dependencies:

- [nlohmann/json](https://github.com/nlohmann/json) - JSON parsing
- [yaml-cpp](https://github.com/jbeder/yaml-cpp) - YAML configuration
- [spdlog](https://github.com/gabime/spdlog) - Logging
- [CLI11](https://github.com/CLIUtils/CLI11) - Command line parsing
- [cpp-httplib](https://github.com/yhirose/cpp-httplib) - HTTP client

## Building for Different Platforms

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential cmake git
# Then follow standard build instructions
```

### Fedora/CentOS
```bash
sudo dnf install gcc-c++ cmake git
# Then follow standard build instructions
```

### Arch Linux
```bash
sudo pacman -S base-devel cmake git
# Then follow standard build instructions
```

### Windows
Use Visual Studio 2019 or later with CMake support, or install CMake and a compatible compiler.

### macOS
```bash
brew install cmake
# Then follow standard build instructions
```

## Contributing

Contributions are welcome! Please read [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

This tool is for educational and personal use only. Please respect Pixiv's terms of service and copyright laws. The authors are not responsible for any misuse of this software.

## Support

- **Issues**: Report bugs and request features on [GitHub Issues](https://github.com/your-username/PixivTagDownloader-CPP/issues)
- **Email**: [<EMAIL>](mailto:<EMAIL>)

## Acknowledgments

- Pixiv for providing the platform and API
- All contributors and users of this project
- The open-source libraries that make this project possible
