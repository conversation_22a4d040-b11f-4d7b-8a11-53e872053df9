#include "types.h"
#include <algorithm>
#include <stdexcept>

namespace pixiv {

std::string to_string(ArtworkType type) {
    switch (type) {
        case ArtworkType::Illust: return "Illust";
        case ArtworkType::Manga: return "Manga";
        case ArtworkType::Novel: return "Novel";
        default: return "Unknown";
    }
}

std::string to_string(TagLogic logic) {
    switch (logic) {
        case TagLogic::AND: return "and";
        case TagLogic::OR: return "or";
        case TagLogic::NOT: return "not";
        default: return "unknown";
    }
}

std::string to_string(DownloadMethod method) {
    switch (method) {
        case DownloadMethod::Direct: return "direct";
        case DownloadMethod::Aria2c: return "aria2c";
        case DownloadMethod::Aria2RPC: return "aria2rpc";
        default: return "unknown";
    }
}

std::string to_string(ConflictStrategy strategy) {
    switch (strategy) {
        case ConflictStrategy::Skip: return "skip";
        case ConflictStrategy::Overwrite: return "overwrite";
        case ConflictStrategy::Rename: return "rename";
        default: return "unknown";
    }
}

std::string to_string(LogLevel level) {
    switch (level) {
        case LogLevel::Trace: return "trace";
        case LogLevel::Debug: return "debug";
        case LogLevel::Info: return "info";
        case LogLevel::Warn: return "warn";
        case LogLevel::Error: return "error";
        default: return "unknown";
    }
}

ArtworkType artwork_type_from_string(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "illust") return ArtworkType::Illust;
    if (lower_str == "manga") return ArtworkType::Manga;
    if (lower_str == "novel") return ArtworkType::Novel;
    
    throw std::invalid_argument("Invalid artwork type: " + str);
}

TagLogic tag_logic_from_string(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "and") return TagLogic::AND;
    if (lower_str == "or") return TagLogic::OR;
    if (lower_str == "not") return TagLogic::NOT;
    
    throw std::invalid_argument("Invalid tag logic: " + str);
}

DownloadMethod download_method_from_string(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "direct") return DownloadMethod::Direct;
    if (lower_str == "aria2c") return DownloadMethod::Aria2c;
    if (lower_str == "aria2rpc") return DownloadMethod::Aria2RPC;
    
    throw std::invalid_argument("Invalid download method: " + str);
}

ConflictStrategy conflict_strategy_from_string(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "skip") return ConflictStrategy::Skip;
    if (lower_str == "overwrite") return ConflictStrategy::Overwrite;
    if (lower_str == "rename") return ConflictStrategy::Rename;
    
    throw std::invalid_argument("Invalid conflict strategy: " + str);
}

LogLevel log_level_from_string(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "trace") return LogLevel::Trace;
    if (lower_str == "debug") return LogLevel::Debug;
    if (lower_str == "info") return LogLevel::Info;
    if (lower_str == "warn") return LogLevel::Warn;
    if (lower_str == "error") return LogLevel::Error;
    
    throw std::invalid_argument("Invalid log level: " + str);
}

} // namespace pixiv
