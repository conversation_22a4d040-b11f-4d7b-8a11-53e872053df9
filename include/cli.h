#pragma once

#include "types.h"
#include <string>
#include <vector>
#include <map>
#include <functional>

namespace pixiv {

/**
 * @brief Command line argument parser
 * 
 * This class handles parsing and validation of command line arguments.
 */
class ArgumentParser {
public:
    /**
     * @brief Constructor
     */
    ArgumentParser();

    /**
     * @brief Destructor
     */
    ~ArgumentParser();

    /**
     * @brief Parse command line arguments
     * @param argc Argument count
     * @param argv Argument values
     * @return true if parsing successful
     */
    bool parse(int argc, char* argv[]);

    /**
     * @brief Get parsed arguments as map
     * @return Map of argument name to value
     */
    std::map<std::string, std::string> get_arguments() const;

    /**
     * @brief Check if help was requested
     * @return true if help flag was provided
     */
    bool help_requested() const;

    /**
     * @brief Get help text
     * @return Help text string
     */
    std::string get_help_text() const;

    /**
     * @brief Get parsing errors
     * @return Vector of error messages
     */
    std::vector<std::string> get_errors() const;

private:
    /**
     * @brief Setup command line options
     */
    void setup_options();

    std::map<std::string, std::string> arguments_;
    std::vector<std::string> errors_;
    bool help_requested_;
    std::string help_text_;
};

/**
 * @brief Interactive user interface
 * 
 * This class provides interactive command line interface for user input.
 */
class InteractiveUI {
public:
    /**
     * @brief Constructor
     */
    InteractiveUI();

    /**
     * @brief Destructor
     */
    ~InteractiveUI();

    /**
     * @brief Get user ID from user input
     * @return User ID string
     */
    std::string get_user_id();

    /**
     * @brief Get tags selection from user
     * @param artworks Vector of artworks to extract tags from (only when needed)
     * @return Selected tags and logic
     */
    std::pair<std::vector<std::string>, TagLogic> get_tags_selection(
        const std::vector<ArtworkInfo>& artworks);

    /**
     * @brief Get artwork types selection from user
     * @return Selected artwork types
     */
    std::vector<ArtworkType> get_artwork_types_selection();

    /**
     * @brief Confirm download with user
     * @param artwork_count Number of artworks to download
     * @param total_files Number of total files
     * @return true if user confirms
     */
    bool confirm_download(int artwork_count, int total_files);

    /**
     * @brief Display progress during download
     * @param progress Download progress information
     */
    void display_progress(const DownloadProgress& progress);

    /**
     * @brief Display completion message
     * @param success true if download completed successfully
     * @param stats Download statistics
     */
    void display_completion(bool success, const std::map<std::string, int>& stats);

    /**
     * @brief Display error message
     * @param error Error message
     */
    void display_error(const std::string& error);

    /**
     * @brief Display warning message
     * @param warning Warning message
     */
    void display_warning(const std::string& warning);

    /**
     * @brief Display info message
     * @param info Info message
     */
    void display_info(const std::string& info);

private:
    /**
     * @brief Get user input with prompt
     * @param prompt Prompt message
     * @return User input string
     */
    std::string get_user_input(const std::string& prompt);

    /**
     * @brief Get yes/no confirmation from user
     * @param prompt Prompt message
     * @return true if user confirms
     */
    bool get_confirmation(const std::string& prompt);

    /**
     * @brief Display paginated list and get selection
     * @param items Vector of items to display
     * @param page_size Number of items per page
     * @param allow_multiple Allow multiple selection
     * @return Vector of selected indices
     */
    std::vector<int> display_paginated_selection(const std::vector<std::string>& items,
                                                int page_size = 20,
                                                bool allow_multiple = true);

    /**
     * @brief Search items by keyword
     * @param items Vector of items to search
     * @param keyword Search keyword
     * @return Vector of matching indices
     */
    std::vector<int> search_items(const std::vector<std::string>& items,
                                 const std::string& keyword);

    /**
     * @brief Display progress bar
     * @param current Current progress value
     * @param total Total progress value
     * @param width Progress bar width
     * @return Progress bar string
     */
    std::string create_progress_bar(int current, int total, int width = 50);

    /**
     * @brief Format file size for display
     * @param bytes File size in bytes
     * @return Formatted size string
     */
    std::string format_file_size(size_t bytes);

    /**
     * @brief Format duration for display
     * @param duration Duration in milliseconds
     * @return Formatted duration string
     */
    std::string format_duration(std::chrono::milliseconds duration);

    bool use_colors_;
    std::string last_progress_line_;
};

/**
 * @brief Progress display manager
 * 
 * This class manages real-time progress display during downloads.
 */
class ProgressDisplay {
public:
    /**
     * @brief Constructor
     * @param interactive true if running in interactive mode
     */
    explicit ProgressDisplay(bool interactive = true);

    /**
     * @brief Destructor
     */
    ~ProgressDisplay();

    /**
     * @brief Update progress display
     * @param progress Download progress information
     */
    void update(const DownloadProgress& progress);

    /**
     * @brief Finish progress display
     */
    void finish();

    /**
     * @brief Set update interval
     * @param interval_ms Update interval in milliseconds
     */
    void set_update_interval(int interval_ms);

private:
    /**
     * @brief Clear current line
     */
    void clear_line();

    /**
     * @brief Move cursor to beginning of line
     */
    void move_to_line_start();

    /**
     * @brief Create progress display string
     * @param progress Progress information
     * @return Progress display string
     */
    std::string create_progress_string(const DownloadProgress& progress);

    bool interactive_;
    bool finished_;
    int update_interval_ms_;
    std::chrono::steady_clock::time_point last_update_;
    std::string last_display_;
};

/**
 * @brief Console color utilities
 */
namespace console {
    /**
     * @brief Color codes for console output
     */
    enum class Color {
        Reset,
        Red,
        Green,
        Yellow,
        Blue,
        Magenta,
        Cyan,
        White
    };

    /**
     * @brief Get color code string
     * @param color Color enum
     * @return ANSI color code string
     */
    std::string color_code(Color color);

    /**
     * @brief Colorize text
     * @param text Text to colorize
     * @param color Text color
     * @return Colorized text string
     */
    std::string colorize(const std::string& text, Color color);

    /**
     * @brief Check if console supports colors
     * @return true if colors are supported
     */
    bool supports_colors();
}

} // namespace pixiv
