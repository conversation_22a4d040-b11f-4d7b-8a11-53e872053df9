#pragma once

#include "types.h"
#include <string>
#include <vector>
#include <chrono>
#include <map>

namespace pixiv {
namespace utils {

/**
 * @brief String utilities
 */
namespace string {
    /**
     * @brief Make a string safe for use as filename
     * @param input Input string
     * @return Filename-safe string
     */
    std::string make_filename_safe(const std::string& input);

    /**
     * @brief Split string by delimiter
     * @param input Input string
     * @param delimiter Delimiter character
     * @return Vector of split strings
     */
    std::vector<std::string> split(const std::string& input, char delimiter);

    /**
     * @brief Join strings with delimiter
     * @param strings Vector of strings to join
     * @param delimiter Delimiter string
     * @return Joined string
     */
    std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Trim whitespace from both ends
     * @param input Input string
     * @return Trimmed string
     */
    std::string trim(const std::string& input);

    /**
     * @brief Convert string to lowercase
     * @param input Input string
     * @return Lowercase string
     */
    std::string to_lower(const std::string& input);

    /**
     * @brief Replace all occurrences of substring
     * @param input Input string
     * @param from Substring to replace
     * @param to Replacement string
     * @return String with replacements
     */
    std::string replace_all(const std::string& input, const std::string& from, const std::string& to);

    /**
     * @brief Check if string starts with prefix
     * @param input Input string
     * @param prefix Prefix to check
     * @return true if starts with prefix
     */
    bool starts_with(const std::string& input, const std::string& prefix);

    /**
     * @brief Check if string ends with suffix
     * @param input Input string
     * @param suffix Suffix to check
     * @return true if ends with suffix
     */
    bool ends_with(const std::string& input, const std::string& suffix);
}

/**
 * @brief Time utilities
 */
namespace time {
    /**
     * @brief Format time point to string
     * @param time_point Time point to format
     * @param format Format string (strftime compatible)
     * @return Formatted time string
     */
    std::string format_time(const std::chrono::system_clock::time_point& time_point, 
                           const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Parse ISO 8601 time string
     * @param time_str ISO 8601 time string
     * @return Time point
     */
    std::chrono::system_clock::time_point parse_iso8601(const std::string& time_str);

    /**
     * @brief Get current time as ISO 8601 string
     * @return Current time in ISO 8601 format
     */
    std::string current_iso8601();
}

/**
 * @brief File system utilities
 */
namespace fs {
    /**
     * @brief Check if file exists
     * @param path File path
     * @return true if file exists
     */
    bool file_exists(const std::string& path);

    /**
     * @brief Check if directory exists
     * @param path Directory path
     * @return true if directory exists
     */
    bool directory_exists(const std::string& path);

    /**
     * @brief Create directory recursively
     * @param path Directory path
     * @return true if created successfully or already exists
     */
    bool create_directories(const std::string& path);

    /**
     * @brief Get file size
     * @param path File path
     * @return File size in bytes, -1 if error
     */
    long long get_file_size(const std::string& path);

    /**
     * @brief Get directory separator for current platform
     * @return Directory separator character
     */
    char get_path_separator();

    /**
     * @brief Join path components
     * @param components Path components
     * @return Joined path
     */
    std::string join_path(const std::vector<std::string>& components);

    /**
     * @brief Get parent directory of path
     * @param path Input path
     * @return Parent directory path
     */
    std::string get_parent_directory(const std::string& path);

    /**
     * @brief Get filename from path
     * @param path Input path
     * @return Filename
     */
    std::string get_filename(const std::string& path);

    /**
     * @brief Get file extension
     * @param path Input path
     * @return File extension (including dot)
     */
    std::string get_extension(const std::string& path);
}

/**
 * @brief Template utilities for path and filename generation
 */
namespace template_engine {
    /**
     * @brief Expand template string with variables
     * @param template_str Template string with {variable} placeholders
     * @param variables Map of variable name to value
     * @return Expanded string
     */
    std::string expand_template(const std::string& template_str, 
                               const std::map<std::string, std::string>& variables);

    /**
     * @brief Generate variables map from artwork info
     * @param artwork Artwork information
     * @param page_index Page index for multi-page artworks (-1 for single page)
     * @param page_format Page index format string (e.g., "p{:02d}")
     * @return Map of template variables
     */
    std::map<std::string, std::string> generate_artwork_variables(
        const ArtworkInfo& artwork, 
        int page_index = -1,
        const std::string& page_format = "p{:d}");
}

/**
 * @brief HTTP utilities
 */
namespace http {
    /**
     * @brief Parse cookie string into key-value pairs
     * @param cookie_str Cookie string
     * @return Map of cookie name to value
     */
    std::map<std::string, std::string> parse_cookies(const std::string& cookie_str);

    /**
     * @brief Format cookies map into cookie string
     * @param cookies Map of cookie name to value
     * @return Cookie string
     */
    std::string format_cookies(const std::map<std::string, std::string>& cookies);

    /**
     * @brief Extract filename from URL
     * @param url URL string
     * @return Filename extracted from URL
     */
    std::string extract_filename_from_url(const std::string& url);

    /**
     * @brief URL encode string
     * @param input Input string
     * @return URL encoded string
     */
    std::string url_encode(const std::string& input);

    /**
     * @brief URL decode string
     * @param input URL encoded string
     * @return Decoded string
     */
    std::string url_decode(const std::string& input);
}

/**
 * @brief Random utilities
 */
namespace random {
    /**
     * @brief Generate random integer in range [min, max]
     * @param min Minimum value (inclusive)
     * @param max Maximum value (inclusive)
     * @return Random integer
     */
    int random_int(int min, int max);

    /**
     * @brief Generate random delay based on range
     * @param delay_range Pair of min and max delay in seconds
     * @return Random delay in seconds
     */
    int random_delay(const std::pair<int, int>& delay_range);
}

} // namespace utils
} // namespace pixiv
