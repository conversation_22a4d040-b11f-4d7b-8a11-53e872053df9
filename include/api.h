#pragma once

#include "types.h"
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace pixiv {

/**
 * @brief HTTP response structure
 */
struct HttpResponse {
    int status_code;
    std::string body;
    std::map<std::string, std::string> headers;
    bool success;
    std::string error_message;
};

/**
 * @brief HTTP client interface
 */
class HttpClient {
public:
    virtual ~HttpClient() = default;

    /**
     * @brief Make GET request
     * @param url Request URL
     * @param headers Additional headers
     * @return HTTP response
     */
    virtual HttpResponse get(const std::string& url, 
                           const std::map<std::string, std::string>& headers = {}) = 0;

    /**
     * @brief Make POST request
     * @param url Request URL
     * @param data POST data
     * @param headers Additional headers
     * @return HTTP response
     */
    virtual HttpResponse post(const std::string& url, 
                            const std::string& data,
                            const std::map<std::string, std::string>& headers = {}) = 0;

    /**
     * @brief Download file with progress callback
     * @param url File URL
     * @param local_path Local file path
     * @param progress_callback Progress callback function (bytes_downloaded, total_bytes)
     * @param headers Additional headers
     * @return true if download successful
     */
    virtual bool download_file(const std::string& url, 
                             const std::string& local_path,
                             std::function<void(size_t, size_t)> progress_callback = nullptr,
                             const std::map<std::string, std::string>& headers = {}) = 0;
};

/**
 * @brief Pixiv API client
 * 
 * This class provides high-level interface to Pixiv's API endpoints.
 * It handles authentication, rate limiting, and response parsing.
 */
class PixivApi {
public:
    /**
     * @brief Constructor
     * @param http_client HTTP client implementation
     */
    explicit PixivApi(std::unique_ptr<HttpClient> http_client);

    /**
     * @brief Destructor
     */
    ~PixivApi();

    /**
     * @brief Get user information by UID
     * @param uid User ID
     * @return User information, nullptr if failed
     */
    std::unique_ptr<UserInfo> get_user_info(const std::string& uid);

    /**
     * @brief Get all artworks by user UID
     * @param uid User ID
     * @param progress_callback Progress callback function (current_page, total_pages)
     * @return Vector of artwork information
     */
    std::vector<ArtworkInfo> get_user_artworks(const std::string& uid,
                                              std::function<void(int, int)> progress_callback = nullptr);

    /**
     * @brief Get detailed artwork information
     * @param pid Artwork ID
     * @return Artwork information, nullptr if failed
     */
    std::unique_ptr<ArtworkInfo> get_artwork_details(const std::string& pid);

    /**
     * @brief Get all unique tags from user's artworks
     * @param uid User ID
     * @return Vector of unique tags
     */
    std::vector<std::string> get_user_tags(const std::string& uid);

    /**
     * @brief Filter artworks by tags and logic
     * @param artworks Vector of artworks to filter
     * @param tags Tags to filter by
     * @param logic Tag filtering logic
     * @return Filtered vector of artworks
     */
    std::vector<ArtworkInfo> filter_artworks_by_tags(const std::vector<ArtworkInfo>& artworks,
                                                     const std::vector<std::string>& tags,
                                                     TagLogic logic);

    /**
     * @brief Filter artworks by type
     * @param artworks Vector of artworks to filter
     * @param types Artwork types to include
     * @return Filtered vector of artworks
     */
    std::vector<ArtworkInfo> filter_artworks_by_type(const std::vector<ArtworkInfo>& artworks,
                                                     const std::vector<ArtworkType>& types);

    /**
     * @brief Get last error message
     * @return Error message string
     */
    std::string get_last_error() const;

private:
    /**
     * @brief Make authenticated API request
     * @param url Request URL
     * @param method HTTP method (GET/POST)
     * @param data POST data (for POST requests)
     * @return HTTP response
     */
    HttpResponse make_api_request(const std::string& url, 
                                 const std::string& method = "GET",
                                 const std::string& data = "");

    /**
     * @brief Parse user information from JSON response
     * @param json_str JSON response string
     * @return User information, nullptr if parsing failed
     */
    std::unique_ptr<UserInfo> parse_user_info(const std::string& json_str);

    /**
     * @brief Parse artwork list from JSON response
     * @param json_str JSON response string
     * @return Vector of artwork information
     */
    std::vector<ArtworkInfo> parse_artwork_list(const std::string& json_str);

    /**
     * @brief Parse artwork details from JSON response
     * @param json_str JSON response string
     * @return Artwork information, nullptr if parsing failed
     */
    std::unique_ptr<ArtworkInfo> parse_artwork_details(const std::string& json_str);

    /**
     * @brief Check if artwork matches tag filter
     * @param artwork Artwork to check
     * @param tags Tags to match
     * @param logic Tag filtering logic
     * @return true if artwork matches filter
     */
    bool matches_tag_filter(const ArtworkInfo& artwork,
                           const std::vector<std::string>& tags,
                           TagLogic logic);

    /**
     * @brief Apply rate limiting delay
     */
    void apply_rate_limit();

    std::unique_ptr<HttpClient> http_client_;
    std::string last_error_;
    std::chrono::steady_clock::time_point last_request_time_;
};

/**
 * @brief Factory function to create HTTP client
 * @return Unique pointer to HTTP client implementation
 */
std::unique_ptr<HttpClient> create_http_client();

} // namespace pixiv
