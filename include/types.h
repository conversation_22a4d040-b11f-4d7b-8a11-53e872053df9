#pragma once

#include <string>
#include <vector>
#include <map>
#include <chrono>
#include <memory>

namespace pixiv {

/**
 * @brief Artwork types supported by Pixiv
 */
enum class ArtworkType {
    Illust,
    Manga,
    Novel
};

/**
 * @brief Tag filtering logic
 */
enum class TagLogic {
    AND,    // Artwork must contain all specified tags
    OR,     // Artwork must contain any of the specified tags
    NOT     // Artwork must not contain any of the specified tags
};

/**
 * @brief Download methods supported
 */
enum class DownloadMethod {
    Direct,     // Direct download using built-in HTTP client
    Aria2c,     // Download using aria2c command line
    Aria2RPC    // Download using aria2 RPC
};

/**
 * @brief File conflict resolution strategies
 */
enum class ConflictStrategy {
    Skip,       // Skip existing files
    Overwrite,  // Overwrite existing files
    Rename      // Rename new files with suffix
};

/**
 * @brief Log levels
 */
enum class LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error
};

/**
 * @brief User information structure
 */
struct UserInfo {
    std::string uid;
    std::string username;
    std::string safe_username;  // Filename-safe version
};

/**
 * @brief Series information structure
 */
struct SeriesInfo {
    std::string id;
    std::string title;
    std::string safe_title;  // Filename-safe version
};

/**
 * @brief Artwork metadata structure
 */
struct ArtworkInfo {
    std::string pid;
    ArtworkType type;
    std::string title;
    std::string safe_title;     // Filename-safe version
    std::vector<std::string> tags;
    std::string description;
    std::chrono::system_clock::time_point upload_date;
    std::vector<std::string> image_urls;
    std::string novel_content;  // For novels only
    int page_count;
    bool is_r18;
    int like_count;
    int bookmark_count;
    int word_count;             // For novels only
    UserInfo author;
    std::shared_ptr<SeriesInfo> series;  // nullptr if not part of series
};

/**
 * @brief Download task structure
 */
struct DownloadTask {
    std::string url;
    std::string local_path;
    std::string filename;
    ArtworkInfo artwork;
    int page_index;             // For multi-page artworks
    bool is_metadata_file;      // True for .txt metadata files
};

/**
 * @brief Download progress information
 */
struct DownloadProgress {
    int total_artworks;
    int completed_artworks;
    int total_files;
    int completed_files;
    int failed_files;
    size_t total_bytes;
    size_t downloaded_bytes;
    size_t current_file_bytes;
    size_t current_file_total_bytes;
    std::string current_file;
    double current_file_progress;
};

/**
 * @brief Configuration structure for path templates
 */
struct PathTemplate {
    std::string directory_template;      // Main directory structure template
    std::string filename_template;       // Filename template
    std::string subdirectory_template;   // Template for multi-page artwork subdirectories
    std::string page_index_format;       // e.g., "p{:02d}" for p00, p01, etc.
    std::string novel_content_separator; // e.g., "*** Content ***"
};

/**
 * @brief HTTP request configuration
 */
struct HttpConfig {
    std::string user_agent;
    std::string referer;
    std::map<std::string, std::string> headers;
    int timeout_seconds;
    int max_retries;
    int retry_delay_seconds;
    bool verify_ssl;
};

/**
 * @brief Aria2 RPC configuration
 */
struct Aria2Config {
    std::string rpc_url;
    std::string secret;
    bool verify_ssl;
    std::string ca_cert_path;
};

/**
 * @brief Download configuration
 */
struct DownloadConfig {
    DownloadMethod method;
    int concurrency;
    std::pair<int, int> delay_range;    // min, max delay in seconds
    ConflictStrategy conflict_strategy;
    int timeout_minutes;                // Overall download timeout in minutes
    Aria2Config aria2;
};

/**
 * @brief Utility functions for enum conversions
 */
std::string to_string(ArtworkType type);
std::string to_string(TagLogic logic);
std::string to_string(DownloadMethod method);
std::string to_string(ConflictStrategy strategy);
std::string to_string(LogLevel level);

ArtworkType artwork_type_from_string(const std::string& str);
TagLogic tag_logic_from_string(const std::string& str);
DownloadMethod download_method_from_string(const std::string& str);
ConflictStrategy conflict_strategy_from_string(const std::string& str);
LogLevel log_level_from_string(const std::string& str);

} // namespace pixiv
