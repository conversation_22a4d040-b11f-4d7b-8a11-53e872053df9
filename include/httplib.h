#pragma once

// Real HTTP client implementation using libcurl
#include <string>
#include <map>
#include <memory>
#include <functional>
#include <curl/curl.h>

namespace httplib {

using Headers = std::map<std::string, std::string>;

struct Response {
    int status = 0;
    std::string body;
    Headers headers;
    
    operator bool() const { return status > 0; }
};

// Forward declaration for callback data
struct CurlCallbackData {
    std::string* response_body;
    Headers* response_headers;
    std::function<bool(const char*, size_t)>* content_receiver;
    bool header_parsing_done;

    CurlCallbackData() : response_body(nullptr), response_headers(nullptr),
                        content_receiver(nullptr), header_parsing_done(false) {}
};

class Client {
public:
    Client(const std::string& host);
    ~Client();

    void set_connection_timeout(int timeout_sec);
    void set_read_timeout(int timeout_sec);

    std::shared_ptr<Response> Get(const std::string& path, const Headers& headers = {});

    std::shared_ptr<Response> Get(const std::string& path, const Headers& headers,
                                 std::function<bool(const char*, size_t)> content_receiver);

    std::shared_ptr<Response> Post(const std::string& path, const Headers& headers,
                                  const std::string& body, const std::string& content_type);

private:
    std::string host_;
    int connection_timeout_;
    int read_timeout_;

    // CURL callback functions
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, CurlCallbackData* data);
    static size_t HeaderCallback(char* buffer, size_t size, size_t nitems, CurlCallbackData* data);

    // Helper methods
    CURL* setup_curl_handle(const std::string& url, const Headers& headers, CurlCallbackData* callback_data);
    std::shared_ptr<Response> execute_request(CURL* curl, CurlCallbackData* callback_data);
};

} // namespace httplib
