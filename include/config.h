#pragma once

#include "types.h"
#include <string>
#include <vector>
#include <map>
#include <memory>

namespace pixiv {

/**
 * @brief Main configuration class for PixivTagDownloader
 * 
 * This class manages all configuration settings including:
 * - Authentication (cookies)
 * - Download settings
 * - Path templates
 * - HTTP settings
 * - Logging settings
 */
class Config {
public:
    /**
     * @brief Default constructor
     */
    Config();

    /**
     * @brief Load configuration from YAML file
     * @param config_path Path to config.yaml file
     * @return true if loaded successfully, false otherwise
     */
    bool load_from_file(const std::string& config_path);

    /**
     * @brief Override configuration with command line arguments
     * @param args Map of argument name to value
     */
    void override_with_args(const std::map<std::string, std::string>& args);

    /**
     * @brief Validate configuration settings
     * @return true if configuration is valid, false otherwise
     */
    bool validate() const;

    /**
     * @brief Get error messages from validation
     * @return Vector of error messages
     */
    std::vector<std::string> get_validation_errors() const;

    // Authentication settings
    std::string cookie;

    // Target settings
    std::string target_uid;
    std::vector<std::string> tags;
    TagLogic tag_logic;
    std::vector<ArtworkType> artwork_types;
    bool download_all;

    // Output settings
    std::string output_dir;
    PathTemplate path_template;

    // Download settings
    DownloadConfig download;

    // HTTP settings
    HttpConfig http;

    // Logging settings
    LogLevel log_level;
    std::string log_file;
    bool log_to_console;

private:
    /**
     * @brief Load default configuration values
     */
    void load_defaults();

    /**
     * @brief Parse artwork types from string
     * @param types_str Comma-separated artwork types
     * @return Vector of ArtworkType enums
     */
    std::vector<ArtworkType> parse_artwork_types(const std::string& types_str) const;

    /**
     * @brief Parse delay range from string
     * @param delay_str Delay range string (e.g., "1-3")
     * @return Pair of min and max delay
     */
    std::pair<int, int> parse_delay_range(const std::string& delay_str) const;

    mutable std::vector<std::string> validation_errors_;
};

/**
 * @brief Global configuration instance
 * 
 * This provides a singleton-like access to configuration throughout the application.
 * The instance should be initialized once at startup and then accessed read-only.
 */
class ConfigManager {
public:
    /**
     * @brief Initialize the global configuration
     * @param config_path Path to configuration file
     * @param cmd_args Command line arguments override
     * @return true if initialization successful, false otherwise
     */
    static bool initialize(const std::string& config_path, 
                          const std::map<std::string, std::string>& cmd_args = {});

    /**
     * @brief Get the global configuration instance
     * @return Reference to the global configuration
     * @throws std::runtime_error if not initialized
     */
    static const Config& get();

    /**
     * @brief Check if configuration is initialized
     * @return true if initialized, false otherwise
     */
    static bool is_initialized();

    /**
     * @brief Reset the configuration (mainly for testing)
     */
    static void reset();

private:
    static std::unique_ptr<Config> instance_;
    static bool initialized_;
};

} // namespace pixiv
