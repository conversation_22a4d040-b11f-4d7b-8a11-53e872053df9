#pragma once

#include "types.h"
#include <string>
#include <map>

namespace pixiv {

/**
 * @brief Authentication manager for Pixiv
 * 
 * This class handles cookie-based authentication with Pixiv.
 * It validates cookies by making test requests to authenticated endpoints.
 */
class AuthManager {
public:
    /**
     * @brief Constructor
     */
    AuthManager();

    /**
     * @brief Destructor
     */
    ~AuthManager();

    /**
     * @brief Set authentication cookie
     * @param cookie_str Cookie string in HTTP format
     * @return true if cookie format is valid
     */
    bool set_cookie(const std::string& cookie_str);

    /**
     * @brief Validate current authentication
     * @return true if authentication is valid
     */
    bool validate_authentication();

    /**
     * @brief Get authentication headers for HTTP requests
     * @return Map of header name to value
     */
    std::map<std::string, std::string> get_auth_headers() const;

    /**
     * @brief Check if authenticated
     * @return true if authenticated
     */
    bool is_authenticated() const;

    /**
     * @brief Get last error message
     * @return Error message string
     */
    std::string get_last_error() const;

    /**
     * @brief Get user ID from authenticated session
     * @return User ID string, empty if not authenticated
     */
    std::string get_authenticated_user_id() const;

private:
    /**
     * @brief Parse cookie string into components
     * @param cookie_str Cookie string
     * @return true if parsing successful
     */
    bool parse_cookie_string(const std::string& cookie_str);

    /**
     * @brief Make test request to validate authentication
     * @return true if request successful and authenticated
     */
    bool test_authentication();

    /**
     * @brief Extract user ID from authentication test response
     * @param response_body Response body from test request
     * @return User ID string, empty if not found
     */
    std::string extract_user_id_from_response(const std::string& response_body) const;

    std::map<std::string, std::string> cookies_;
    bool authenticated_;
    std::string last_error_;
    std::string authenticated_user_id_;
};

/**
 * @brief Global authentication manager instance
 * 
 * Provides singleton-like access to authentication throughout the application.
 */
class AuthenticationManager {
public:
    /**
     * @brief Initialize global authentication
     * @param cookie_str Cookie string
     * @return true if initialization successful
     */
    static bool initialize(const std::string& cookie_str);

    /**
     * @brief Get global authentication manager
     * @return Reference to global auth manager
     * @throws std::runtime_error if not initialized
     */
    static const AuthManager& get();

    /**
     * @brief Check if authentication is initialized
     * @return true if initialized
     */
    static bool is_initialized();

    /**
     * @brief Reset authentication (mainly for testing)
     */
    static void reset();

private:
    static std::unique_ptr<AuthManager> instance_;
    static bool initialized_;
};

} // namespace pixiv
