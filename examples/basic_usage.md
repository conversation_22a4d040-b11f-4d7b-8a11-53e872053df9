# PixivTagDownloader - Basic Usage Examples

This document provides basic usage examples for PixivTagDownloader.

## Prerequisites

1. **Build the project** (see README.md for detailed instructions)
2. **Get your Pixiv session cookie**:
   - Login to Pixiv in your browser
   - Open Developer Tools (F12)
   - Go to Application/Storage → Cookies → https://www.pixiv.net
   - Copy the entire cookie string
3. **Configure the application**:
   ```bash
   cp config.yaml.example config.yaml
   # Edit config.yaml and paste your cookie
   ```

## Interactive Mode

The easiest way to use PixivTagDownloader is through interactive mode:

```bash
./PixivTagDownloader
```

The program will guide you through:
1. Entering the target user ID
2. Selecting tags (if desired)
3. Choosing artwork types
4. Confirming the download

## Command Line Mode

For automation or scripting, use command line mode:

### Download All Artworks

```bash
# Download all artworks from user 123456
./PixivTagDownloader -u 123456 --all
```

### Download with Tag Filtering

```bash
# Download artworks that contain ANY of the specified tags
./PixivTagDownloader -u 123456 -t "original,illustration,anime" -l or

# Download artworks that contain ALL of the specified tags
./PixivTagDownloader -u 123456 -t "original,illustration" -l and

# Download artworks that do NOT contain any of the specified tags
./PixivTagDownloader -u 123456 -t "R-18,gore" -l not
```

### Download Specific Artwork Types

```bash
# Download only illustrations
./PixivTagDownloader -u 123456 -T "Illust"

# Download illustrations and manga (no novels)
./PixivTagDownloader -u 123456 -T "Illust,Manga"

# Download only novels
./PixivTagDownloader -u 123456 -T "Novel"
```

### Custom Output Directory

```bash
# Download to a specific directory
./PixivTagDownloader -u 123456 --all --output-dir "/path/to/downloads"
```

### Advanced Options

```bash
# Use custom configuration file
./PixivTagDownloader -c "/path/to/custom/config.yaml" -u 123456 --all

# Control download behavior
./PixivTagDownloader -u 123456 --all \
    --threads 8 \
    --delay 2-5 \
    --skip-existing \
    --log-level debug

# Use aria2 for downloading (requires aria2c installed)
./PixivTagDownloader -u 123456 --all --download-method aria2c

# Use aria2 RPC (requires aria2 daemon running)
./PixivTagDownloader -u 123456 --all \
    --download-method aria2rpc \
    --aria2rpc-url "ws://localhost:6800/jsonrpc" \
    --aria2rpc-secret "your_secret"
```

## Configuration Examples

### Basic Configuration

```yaml
# config.yaml
cookie: "your_pixiv_session_cookie_here"
output_dir: "Downloads"

path_template:
  directory: "{uid}_{username}/{type}"
  filename: "{upload_date}_{pid}_{title}"

download:
  method: "direct"
  concurrency: 4
  delay_range: "1-3"
  conflict_strategy: "skip"

logging:
  level: "info"
  console: true
```

### Advanced Configuration

```yaml
# config.yaml
cookie: "your_pixiv_session_cookie_here"
output_dir: "/home/<USER>/PixivDownloads"

path_template:
  directory: "{uid}_{username}/{type}/{series_title}"
  filename: "{upload_date}_{pid}_{title}{page_index}"
  page_index_format: "p{:02d}"

download:
  method: "aria2rpc"
  concurrency: 8
  delay_range: "2-5"
  conflict_strategy: "rename"
  
  aria2:
    rpc_url: "ws://localhost:6800/jsonrpc"
    secret: "my_aria2_secret"
    verify_ssl: true

http:
  timeout_seconds: 60
  max_retries: 5
  retry_delay_seconds: 2

logging:
  level: "debug"
  file: "pixiv_downloader.log"
  console: true
```

## File Organization

Downloaded files are organized according to your path templates:

```
Downloads/
├── 123456_ArtistName/
│   ├── Illust/
│   │   ├── SeriesName/
│   │   │   ├── 20240101_98765432_ArtworkTitle.jpg
│   │   │   ├── 20240101_98765432_ArtworkTitle_metadata.txt
│   │   │   └── ...
│   │   └── No_Series/
│   ├── Manga/
│   │   ├── 20240102_98765433_MangaTitlep00.jpg
│   │   ├── 20240102_98765433_MangaTitlep01.jpg
│   │   ├── 20240102_98765433_MangaTitle_metadata.txt
│   │   └── ...
│   └── Novel/
│       ├── 20240103_98765434_NovelTitle.txt
│       ├── 20240103_98765434_NovelTitle_metadata.txt
│       └── ...
└── ...
```

## Troubleshooting

### Authentication Issues

```bash
# Test your configuration
./PixivTagDownloader -u 123456 --all --log-level debug
```

If you see authentication errors:
1. Make sure your cookie is valid and complete
2. Try logging out and back into Pixiv to get a fresh cookie
3. Check that the cookie includes all necessary fields

### Download Issues

```bash
# Enable debug logging to see detailed error messages
./PixivTagDownloader -u 123456 --all --log-level debug

# Try with reduced concurrency
./PixivTagDownloader -u 123456 --all --threads 1 --delay 5-10
```

### Performance Optimization

```bash
# For fast downloads with good connection
./PixivTagDownloader -u 123456 --all --threads 8 --delay 1-2

# For slow/unstable connection
./PixivTagDownloader -u 123456 --all --threads 2 --delay 3-8
```

## Getting Help

```bash
# Show all available options
./PixivTagDownloader --help

# Show version information
./PixivTagDownloader --version
```

For more advanced usage and configuration options, see the main README.md file.
