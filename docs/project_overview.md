# PixivTagDownloader - Project Overview

## Project Description

PixivTagDownloader is a powerful C++ application designed to download artworks from Pixiv based on user ID and tag filters. It provides both interactive and command-line interfaces, making it suitable for both casual users and automation scenarios.

## Key Features

### Core Functionality
- **User-based Downloads**: Download all artworks from specific Pixiv users
- **Tag Filtering**: Filter artworks using AND/OR/NOT logic with multiple tags
- **Multiple Artwork Types**: Support for Illustrations, Manga, and Novels
- **Flexible Organization**: Highly customizable directory structure and file naming

### Download Methods
- **Direct Download**: Built-in HTTP client for direct downloads
- **Aria2c Integration**: Command-line aria2c support for enhanced download capabilities
- **Aria2 RPC**: Integration with aria2 daemon for advanced download management

### User Interface
- **Interactive Mode**: User-friendly guided interface for easy operation
- **Command Line Mode**: Full command-line interface for automation and scripting
- **Progress Tracking**: Real-time progress display with detailed statistics

### Advanced Features
- **Concurrent Downloads**: Multi-threaded downloading with configurable concurrency
- **Rate Limiting**: Configurable delays to respect server limits
- **Conflict Resolution**: Multiple strategies for handling existing files
- **Comprehensive Logging**: Detailed logging with multiple levels and output options
- **Cross-platform**: Supports Windows, Linux, and macOS

## Architecture

### Module Structure

```
PixivTagDownloader/
├── Authentication Module    # Cookie-based Pixiv authentication
├── API Module              # Pixiv API interaction and data parsing
├── Download Module          # Multi-method download management
├── Storage Module           # File organization and metadata generation
├── Configuration Module     # YAML-based configuration management
├── CLI Module              # Command-line and interactive interfaces
├── Core Logic Module       # Main application orchestration
└── Utilities Module        # Common utility functions
```

### Key Components

1. **AuthManager**: Handles Pixiv session authentication using cookies
2. **PixivApi**: Manages API calls and response parsing
3. **DownloadManager**: Orchestrates concurrent downloads with producer-consumer pattern
4. **StorageManager**: Handles file organization and metadata generation
5. **ConfigManager**: Manages application configuration and validation
6. **InteractiveUI**: Provides user-friendly interactive interface

### Design Patterns

- **Factory Pattern**: For creating different downloader implementations
- **Producer-Consumer**: For efficient concurrent download management
- **Singleton**: For global configuration and authentication management
- **Template Method**: For customizable path and filename generation

## Technical Specifications

### Requirements
- **C++ Standard**: C++17 or later
- **Compiler**: GCC ≥ 9, Clang ≥ 10, MSVC ≥ 2019
- **Build System**: CMake ≥ 3.15
- **Dependencies**: nlohmann/json, yaml-cpp, spdlog, fmt

### Performance Characteristics
- **Memory Efficient**: Streaming downloads with minimal memory footprint
- **Scalable**: Configurable concurrency based on system capabilities
- **Robust**: Comprehensive error handling and retry mechanisms
- **Fast**: Optimized for high-throughput downloads

### Security Features
- **Secure Authentication**: Cookie-based authentication without storing credentials
- **Input Validation**: Comprehensive validation of user inputs and configuration
- **Safe File Operations**: Protected file operations with conflict resolution
- **Privacy Conscious**: No logging of sensitive authentication data

## Configuration System

### YAML-based Configuration
The application uses a flexible YAML configuration system supporting:

- **Authentication Settings**: Pixiv session cookies
- **Download Preferences**: Methods, concurrency, delays, conflict resolution
- **Path Templates**: Customizable directory and filename patterns
- **HTTP Settings**: Timeouts, retries, custom headers
- **Logging Configuration**: Levels, outputs, file locations

### Template Variables
Extensive template variable support for file organization:
- User information: `{uid}`, `{username}`
- Artwork details: `{pid}`, `{title}`, `{type}`, `{tags}`
- Temporal data: `{upload_date}`
- Series information: `{series_title}`, `{series_id}`
- Statistics: `{like_count}`, `{bookmark_count}`

## Build System

### Cross-platform Build Support
- **CMake Configuration**: Modern CMake with automatic dependency detection
- **Package Manager Integration**: Support for system package managers
- **Automated Scripts**: Platform-specific dependency installation scripts
- **Continuous Integration Ready**: Structured for CI/CD pipelines

### Dependency Management
- **System Libraries**: Prefers system-installed packages
- **Fallback Options**: Graceful degradation when dependencies are unavailable
- **Version Compatibility**: Supports multiple versions of dependencies

## Usage Scenarios

### Individual Users
- Download favorite artists' complete portfolios
- Organize collections by tags and series
- Backup personal bookmarks and follows

### Content Creators
- Research and reference collection building
- Batch downloading for inspiration boards
- Automated collection maintenance

### Researchers and Archivists
- Large-scale data collection for academic research
- Cultural preservation and archiving
- Statistical analysis of artwork trends

### Developers and Integrators
- Integration into larger content management systems
- Automated content pipeline components
- Custom workflow automation

## Future Enhancements

### Planned Features
- **Web Interface**: Browser-based management interface
- **Database Integration**: SQLite/PostgreSQL support for metadata storage
- **Advanced Filtering**: More sophisticated tag and metadata filtering
- **Batch Operations**: Multi-user and playlist support
- **Cloud Storage**: Direct upload to cloud storage services

### Extensibility
- **Plugin System**: Modular architecture for custom extensions
- **API Expansion**: Support for additional art platforms
- **Custom Downloaders**: Framework for implementing new download methods
- **Export Formats**: Multiple metadata export formats

## Contributing

The project welcomes contributions in several areas:

### Development
- **Core Features**: New functionality and improvements
- **Platform Support**: Additional operating system support
- **Performance**: Optimization and efficiency improvements
- **Testing**: Unit tests and integration testing

### Documentation
- **User Guides**: Tutorials and how-to documentation
- **API Documentation**: Code documentation and examples
- **Translations**: Multi-language support

### Community
- **Bug Reports**: Issue identification and reproduction
- **Feature Requests**: User experience improvements
- **Support**: Helping other users with setup and usage

## License and Legal

### Open Source License
- **MIT License**: Permissive open-source license
- **Commercial Use**: Allowed for commercial applications
- **Modification**: Free to modify and distribute

### Legal Considerations
- **Terms of Service**: Users must comply with Pixiv's terms of service
- **Copyright Respect**: Downloaded content remains subject to original copyright
- **Rate Limiting**: Built-in respect for server resources and policies
- **Educational Use**: Designed for personal and educational purposes

## Support and Resources

### Documentation
- **README.md**: Quick start and basic usage
- **examples/**: Practical usage examples
- **CONTRIBUTING.md**: Contribution guidelines
- **API Documentation**: Detailed code documentation

### Community
- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Community support and questions
- **Email Support**: Direct developer contact

### Professional Support
- **Custom Development**: Available for specialized requirements
- **Integration Assistance**: Help with system integration
- **Training and Consultation**: Professional services available
