#!/bin/bash

# PixivTagDownloader Build Script
# This script builds the PixivTagDownloader project

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"

echo "PixivTagDownloader Build Script"
echo "==============================="
echo ""

# Parse command line arguments
BUILD_TYPE="Release"
CLEAN_BUILD=false
INSTALL_DEPS=false
JOBS=$(nproc)

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --debug         Build in debug mode (default: release)"
            echo "  --clean         Clean build directory before building"
            echo "  --install-deps  Install system dependencies first"
            echo "  --jobs N        Use N parallel jobs (default: $(nproc))"
            echo "  -h, --help      Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Build in release mode"
            echo "  $0 --debug           # Build in debug mode"
            echo "  $0 --clean --debug   # Clean build in debug mode"
            echo "  $0 --install-deps    # Install dependencies and build"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Install dependencies if requested
if [ "$INSTALL_DEPS" = true ]; then
    echo "Installing system dependencies..."
    
    if command -v apt-get >/dev/null 2>&1; then
        echo "Detected Ubuntu/Debian system"
        bash "$PROJECT_ROOT/build_scripts/ubuntu/install_dependencies.sh"
    elif command -v dnf >/dev/null 2>&1; then
        echo "Detected Fedora/CentOS system"
        bash "$PROJECT_ROOT/build_scripts/fedora/install_dependencies.sh"
    elif command -v pacman >/dev/null 2>&1; then
        echo "Detected Arch Linux system"
        bash "$PROJECT_ROOT/build_scripts/archlinux/install_dependencies.sh"
    else
        echo "Warning: Unknown package manager. Please install dependencies manually:"
        echo "  - C++17 compatible compiler (GCC >= 9, Clang >= 10)"
        echo "  - CMake >= 3.15"
        echo "  - nlohmann/json"
        echo "  - yaml-cpp"
        echo "  - spdlog"
        echo "  - fmt"
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    echo ""
fi

# Clean build directory if requested
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

# Create build directory
echo "Creating build directory..."
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Configure project
echo "Configuring project (Build type: $BUILD_TYPE)..."
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" "$PROJECT_ROOT"

# Build project
echo "Building project with $JOBS parallel jobs..."
make -j"$JOBS"

echo ""
echo "Build completed successfully!"
echo ""
echo "Executable location: $BUILD_DIR/PixivTagDownloader"
echo ""
echo "Next steps:"
echo "1. Copy config.yaml.example to config.yaml"
echo "2. Edit config.yaml and set your Pixiv session cookie"
echo "3. Run the program:"
echo "   # Interactive mode:"
echo "   ./PixivTagDownloader"
echo "   # Command line mode:"
echo "   ./PixivTagDownloader -u <user_id> --all"
echo ""
echo "For help: ./PixivTagDownloader --help"
