# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/wd500g/PixivTagDownloader-CPP

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/wd500g/PixivTagDownloader-CPP/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	/usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	/usr/bin/cpack --config ./CPackSourceConfig.cmake /mnt/wd500g/PixivTagDownloader-CPP/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles /mnt/wd500g/PixivTagDownloader-CPP/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/wd500g/PixivTagDownloader-CPP/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named PixivTagDownloader

# Build rule for target.
PixivTagDownloader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 PixivTagDownloader
.PHONY : PixivTagDownloader

# fast build rule for target.
PixivTagDownloader/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/build
.PHONY : PixivTagDownloader/fast

src/api/http_client.o: src/api/http_client.cpp.o
.PHONY : src/api/http_client.o

# target to build an object file
src/api/http_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/http_client.cpp.o
.PHONY : src/api/http_client.cpp.o

src/api/http_client.i: src/api/http_client.cpp.i
.PHONY : src/api/http_client.i

# target to preprocess a source file
src/api/http_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/http_client.cpp.i
.PHONY : src/api/http_client.cpp.i

src/api/http_client.s: src/api/http_client.cpp.s
.PHONY : src/api/http_client.s

# target to generate assembly for a file
src/api/http_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/http_client.cpp.s
.PHONY : src/api/http_client.cpp.s

src/api/pixiv_api.o: src/api/pixiv_api.cpp.o
.PHONY : src/api/pixiv_api.o

# target to build an object file
src/api/pixiv_api.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o
.PHONY : src/api/pixiv_api.cpp.o

src/api/pixiv_api.i: src/api/pixiv_api.cpp.i
.PHONY : src/api/pixiv_api.i

# target to preprocess a source file
src/api/pixiv_api.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.i
.PHONY : src/api/pixiv_api.cpp.i

src/api/pixiv_api.s: src/api/pixiv_api.cpp.s
.PHONY : src/api/pixiv_api.s

# target to generate assembly for a file
src/api/pixiv_api.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.s
.PHONY : src/api/pixiv_api.cpp.s

src/api/real_http_client.o: src/api/real_http_client.cpp.o
.PHONY : src/api/real_http_client.o

# target to build an object file
src/api/real_http_client.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/real_http_client.cpp.o
.PHONY : src/api/real_http_client.cpp.o

src/api/real_http_client.i: src/api/real_http_client.cpp.i
.PHONY : src/api/real_http_client.i

# target to preprocess a source file
src/api/real_http_client.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/real_http_client.cpp.i
.PHONY : src/api/real_http_client.cpp.i

src/api/real_http_client.s: src/api/real_http_client.cpp.s
.PHONY : src/api/real_http_client.s

# target to generate assembly for a file
src/api/real_http_client.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/api/real_http_client.cpp.s
.PHONY : src/api/real_http_client.cpp.s

src/auth/auth.o: src/auth/auth.cpp.o
.PHONY : src/auth/auth.o

# target to build an object file
src/auth/auth.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth.cpp.o
.PHONY : src/auth/auth.cpp.o

src/auth/auth.i: src/auth/auth.cpp.i
.PHONY : src/auth/auth.i

# target to preprocess a source file
src/auth/auth.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth.cpp.i
.PHONY : src/auth/auth.cpp.i

src/auth/auth.s: src/auth/auth.cpp.s
.PHONY : src/auth/auth.s

# target to generate assembly for a file
src/auth/auth.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/auth/auth.cpp.s
.PHONY : src/auth/auth.cpp.s

src/cli/argument_parser.o: src/cli/argument_parser.cpp.o
.PHONY : src/cli/argument_parser.o

# target to build an object file
src/cli/argument_parser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/argument_parser.cpp.o
.PHONY : src/cli/argument_parser.cpp.o

src/cli/argument_parser.i: src/cli/argument_parser.cpp.i
.PHONY : src/cli/argument_parser.i

# target to preprocess a source file
src/cli/argument_parser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/argument_parser.cpp.i
.PHONY : src/cli/argument_parser.cpp.i

src/cli/argument_parser.s: src/cli/argument_parser.cpp.s
.PHONY : src/cli/argument_parser.s

# target to generate assembly for a file
src/cli/argument_parser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/argument_parser.cpp.s
.PHONY : src/cli/argument_parser.cpp.s

src/cli/console_utils.o: src/cli/console_utils.cpp.o
.PHONY : src/cli/console_utils.o

# target to build an object file
src/cli/console_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/console_utils.cpp.o
.PHONY : src/cli/console_utils.cpp.o

src/cli/console_utils.i: src/cli/console_utils.cpp.i
.PHONY : src/cli/console_utils.i

# target to preprocess a source file
src/cli/console_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/console_utils.cpp.i
.PHONY : src/cli/console_utils.cpp.i

src/cli/console_utils.s: src/cli/console_utils.cpp.s
.PHONY : src/cli/console_utils.s

# target to generate assembly for a file
src/cli/console_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/console_utils.cpp.s
.PHONY : src/cli/console_utils.cpp.s

src/cli/interactive_ui.o: src/cli/interactive_ui.cpp.o
.PHONY : src/cli/interactive_ui.o

# target to build an object file
src/cli/interactive_ui.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/interactive_ui.cpp.o
.PHONY : src/cli/interactive_ui.cpp.o

src/cli/interactive_ui.i: src/cli/interactive_ui.cpp.i
.PHONY : src/cli/interactive_ui.i

# target to preprocess a source file
src/cli/interactive_ui.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/interactive_ui.cpp.i
.PHONY : src/cli/interactive_ui.cpp.i

src/cli/interactive_ui.s: src/cli/interactive_ui.cpp.s
.PHONY : src/cli/interactive_ui.s

# target to generate assembly for a file
src/cli/interactive_ui.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/cli/interactive_ui.cpp.s
.PHONY : src/cli/interactive_ui.cpp.s

src/config/config.o: src/config/config.cpp.o
.PHONY : src/config/config.o

# target to build an object file
src/config/config.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config.cpp.o
.PHONY : src/config/config.cpp.o

src/config/config.i: src/config/config.cpp.i
.PHONY : src/config/config.i

# target to preprocess a source file
src/config/config.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config.cpp.i
.PHONY : src/config/config.cpp.i

src/config/config.s: src/config/config.cpp.s
.PHONY : src/config/config.s

# target to generate assembly for a file
src/config/config.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/config/config.cpp.s
.PHONY : src/config/config.cpp.s

src/core_logic/application_runner.o: src/core_logic/application_runner.cpp.o
.PHONY : src/core_logic/application_runner.o

# target to build an object file
src/core_logic/application_runner.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/application_runner.cpp.o
.PHONY : src/core_logic/application_runner.cpp.o

src/core_logic/application_runner.i: src/core_logic/application_runner.cpp.i
.PHONY : src/core_logic/application_runner.i

# target to preprocess a source file
src/core_logic/application_runner.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/application_runner.cpp.i
.PHONY : src/core_logic/application_runner.cpp.i

src/core_logic/application_runner.s: src/core_logic/application_runner.cpp.s
.PHONY : src/core_logic/application_runner.s

# target to generate assembly for a file
src/core_logic/application_runner.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/application_runner.cpp.s
.PHONY : src/core_logic/application_runner.cpp.s

src/core_logic/pixiv_tag_downloader.o: src/core_logic/pixiv_tag_downloader.cpp.o
.PHONY : src/core_logic/pixiv_tag_downloader.o

# target to build an object file
src/core_logic/pixiv_tag_downloader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/pixiv_tag_downloader.cpp.o
.PHONY : src/core_logic/pixiv_tag_downloader.cpp.o

src/core_logic/pixiv_tag_downloader.i: src/core_logic/pixiv_tag_downloader.cpp.i
.PHONY : src/core_logic/pixiv_tag_downloader.i

# target to preprocess a source file
src/core_logic/pixiv_tag_downloader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/pixiv_tag_downloader.cpp.i
.PHONY : src/core_logic/pixiv_tag_downloader.cpp.i

src/core_logic/pixiv_tag_downloader.s: src/core_logic/pixiv_tag_downloader.cpp.s
.PHONY : src/core_logic/pixiv_tag_downloader.s

# target to generate assembly for a file
src/core_logic/pixiv_tag_downloader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/core_logic/pixiv_tag_downloader.cpp.s
.PHONY : src/core_logic/pixiv_tag_downloader.cpp.s

src/downloader/download_manager.o: src/downloader/download_manager.cpp.o
.PHONY : src/downloader/download_manager.o

# target to build an object file
src/downloader/download_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o
.PHONY : src/downloader/download_manager.cpp.o

src/downloader/download_manager.i: src/downloader/download_manager.cpp.i
.PHONY : src/downloader/download_manager.i

# target to preprocess a source file
src/downloader/download_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.i
.PHONY : src/downloader/download_manager.cpp.i

src/downloader/download_manager.s: src/downloader/download_manager.cpp.s
.PHONY : src/downloader/download_manager.s

# target to generate assembly for a file
src/downloader/download_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.s
.PHONY : src/downloader/download_manager.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/storage/storage_manager.o: src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.o

# target to build an object file
src/storage/storage_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o
.PHONY : src/storage/storage_manager.cpp.o

src/storage/storage_manager.i: src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.i

# target to preprocess a source file
src/storage/storage_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.i
.PHONY : src/storage/storage_manager.cpp.i

src/storage/storage_manager.s: src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.s

# target to generate assembly for a file
src/storage/storage_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.s
.PHONY : src/storage/storage_manager.cpp.s

src/types.o: src/types.cpp.o
.PHONY : src/types.o

# target to build an object file
src/types.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/types.cpp.o
.PHONY : src/types.cpp.o

src/types.i: src/types.cpp.i
.PHONY : src/types.i

# target to preprocess a source file
src/types.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/types.cpp.i
.PHONY : src/types.cpp.i

src/types.s: src/types.cpp.s
.PHONY : src/types.s

# target to generate assembly for a file
src/types.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/types.cpp.s
.PHONY : src/types.cpp.s

src/utils/fs_utils.o: src/utils/fs_utils.cpp.o
.PHONY : src/utils/fs_utils.o

# target to build an object file
src/utils/fs_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/fs_utils.cpp.o
.PHONY : src/utils/fs_utils.cpp.o

src/utils/fs_utils.i: src/utils/fs_utils.cpp.i
.PHONY : src/utils/fs_utils.i

# target to preprocess a source file
src/utils/fs_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/fs_utils.cpp.i
.PHONY : src/utils/fs_utils.cpp.i

src/utils/fs_utils.s: src/utils/fs_utils.cpp.s
.PHONY : src/utils/fs_utils.s

# target to generate assembly for a file
src/utils/fs_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/fs_utils.cpp.s
.PHONY : src/utils/fs_utils.cpp.s

src/utils/http_utils.o: src/utils/http_utils.cpp.o
.PHONY : src/utils/http_utils.o

# target to build an object file
src/utils/http_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/http_utils.cpp.o
.PHONY : src/utils/http_utils.cpp.o

src/utils/http_utils.i: src/utils/http_utils.cpp.i
.PHONY : src/utils/http_utils.i

# target to preprocess a source file
src/utils/http_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/http_utils.cpp.i
.PHONY : src/utils/http_utils.cpp.i

src/utils/http_utils.s: src/utils/http_utils.cpp.s
.PHONY : src/utils/http_utils.s

# target to generate assembly for a file
src/utils/http_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/http_utils.cpp.s
.PHONY : src/utils/http_utils.cpp.s

src/utils/random_utils.o: src/utils/random_utils.cpp.o
.PHONY : src/utils/random_utils.o

# target to build an object file
src/utils/random_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/random_utils.cpp.o
.PHONY : src/utils/random_utils.cpp.o

src/utils/random_utils.i: src/utils/random_utils.cpp.i
.PHONY : src/utils/random_utils.i

# target to preprocess a source file
src/utils/random_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/random_utils.cpp.i
.PHONY : src/utils/random_utils.cpp.i

src/utils/random_utils.s: src/utils/random_utils.cpp.s
.PHONY : src/utils/random_utils.s

# target to generate assembly for a file
src/utils/random_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/random_utils.cpp.s
.PHONY : src/utils/random_utils.cpp.s

src/utils/string_utils.o: src/utils/string_utils.cpp.o
.PHONY : src/utils/string_utils.o

# target to build an object file
src/utils/string_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o
.PHONY : src/utils/string_utils.cpp.o

src/utils/string_utils.i: src/utils/string_utils.cpp.i
.PHONY : src/utils/string_utils.i

# target to preprocess a source file
src/utils/string_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.i
.PHONY : src/utils/string_utils.cpp.i

src/utils/string_utils.s: src/utils/string_utils.cpp.s
.PHONY : src/utils/string_utils.s

# target to generate assembly for a file
src/utils/string_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.s
.PHONY : src/utils/string_utils.cpp.s

src/utils/template_utils.o: src/utils/template_utils.cpp.o
.PHONY : src/utils/template_utils.o

# target to build an object file
src/utils/template_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/template_utils.cpp.o
.PHONY : src/utils/template_utils.cpp.o

src/utils/template_utils.i: src/utils/template_utils.cpp.i
.PHONY : src/utils/template_utils.i

# target to preprocess a source file
src/utils/template_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/template_utils.cpp.i
.PHONY : src/utils/template_utils.cpp.i

src/utils/template_utils.s: src/utils/template_utils.cpp.s
.PHONY : src/utils/template_utils.s

# target to generate assembly for a file
src/utils/template_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/template_utils.cpp.s
.PHONY : src/utils/template_utils.cpp.s

src/utils/time_utils.o: src/utils/time_utils.cpp.o
.PHONY : src/utils/time_utils.o

# target to build an object file
src/utils/time_utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o
.PHONY : src/utils/time_utils.cpp.o

src/utils/time_utils.i: src/utils/time_utils.cpp.i
.PHONY : src/utils/time_utils.i

# target to preprocess a source file
src/utils/time_utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.i
.PHONY : src/utils/time_utils.cpp.i

src/utils/time_utils.s: src/utils/time_utils.cpp.s
.PHONY : src/utils/time_utils.s

# target to generate assembly for a file
src/utils/time_utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/PixivTagDownloader.dir/build.make CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.s
.PHONY : src/utils/time_utils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... PixivTagDownloader"
	@echo "... src/api/http_client.o"
	@echo "... src/api/http_client.i"
	@echo "... src/api/http_client.s"
	@echo "... src/api/pixiv_api.o"
	@echo "... src/api/pixiv_api.i"
	@echo "... src/api/pixiv_api.s"
	@echo "... src/api/real_http_client.o"
	@echo "... src/api/real_http_client.i"
	@echo "... src/api/real_http_client.s"
	@echo "... src/auth/auth.o"
	@echo "... src/auth/auth.i"
	@echo "... src/auth/auth.s"
	@echo "... src/cli/argument_parser.o"
	@echo "... src/cli/argument_parser.i"
	@echo "... src/cli/argument_parser.s"
	@echo "... src/cli/console_utils.o"
	@echo "... src/cli/console_utils.i"
	@echo "... src/cli/console_utils.s"
	@echo "... src/cli/interactive_ui.o"
	@echo "... src/cli/interactive_ui.i"
	@echo "... src/cli/interactive_ui.s"
	@echo "... src/config/config.o"
	@echo "... src/config/config.i"
	@echo "... src/config/config.s"
	@echo "... src/core_logic/application_runner.o"
	@echo "... src/core_logic/application_runner.i"
	@echo "... src/core_logic/application_runner.s"
	@echo "... src/core_logic/pixiv_tag_downloader.o"
	@echo "... src/core_logic/pixiv_tag_downloader.i"
	@echo "... src/core_logic/pixiv_tag_downloader.s"
	@echo "... src/downloader/download_manager.o"
	@echo "... src/downloader/download_manager.i"
	@echo "... src/downloader/download_manager.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/storage/storage_manager.o"
	@echo "... src/storage/storage_manager.i"
	@echo "... src/storage/storage_manager.s"
	@echo "... src/types.o"
	@echo "... src/types.i"
	@echo "... src/types.s"
	@echo "... src/utils/fs_utils.o"
	@echo "... src/utils/fs_utils.i"
	@echo "... src/utils/fs_utils.s"
	@echo "... src/utils/http_utils.o"
	@echo "... src/utils/http_utils.i"
	@echo "... src/utils/http_utils.s"
	@echo "... src/utils/random_utils.o"
	@echo "... src/utils/random_utils.i"
	@echo "... src/utils/random_utils.s"
	@echo "... src/utils/string_utils.o"
	@echo "... src/utils/string_utils.i"
	@echo "... src/utils/string_utils.s"
	@echo "... src/utils/template_utils.o"
	@echo "... src/utils/template_utils.i"
	@echo "... src/utils/template_utils.s"
	@echo "... src/utils/time_utils.o"
	@echo "... src/utils/time_utils.i"
	@echo "... src/utils/time_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

