file(REMOVE_RECURSE
  "CMakeFiles/PixivTagDownloader.dir/link.d"
  "CMakeFiles/PixivTagDownloader.dir/src/api/http_client.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/api/http_client.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/api/pixiv_api.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/api/real_http_client.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/api/real_http_client.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/auth.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/auth/auth.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/argument_parser.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/argument_parser.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/console_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/console_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/interactive_ui.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/cli/interactive_ui.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/config/config.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/core_logic/application_runner.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/core_logic/application_runner.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/core_logic/pixiv_tag_downloader.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/core_logic/pixiv_tag_downloader.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/downloader/download_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/main.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/storage/storage_manager.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/types.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/types.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/fs_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/fs_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/http_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/http_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/random_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/random_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/string_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/template_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/template_utils.cpp.o.d"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o"
  "CMakeFiles/PixivTagDownloader.dir/src/utils/time_utils.cpp.o.d"
  "PixivTagDownloader"
  "PixivTagDownloader.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/PixivTagDownloader.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
