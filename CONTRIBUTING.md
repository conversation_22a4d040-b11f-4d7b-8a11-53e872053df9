# Contributing to PixivTagDownloader

Thank you for your interest in contributing to PixivTagDownloader! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Coding Standards](#coding-standards)
- [Submitting Changes](#submitting-changes)
- [Reporting Issues](#reporting-issues)
- [Feature Requests](#feature-requests)

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow:

- Be respectful and inclusive
- Focus on constructive feedback
- Help maintain a welcoming environment
- Respect different viewpoints and experiences

## Getting Started

### Prerequisites

- C++17 compatible compiler (GCC ≥ 9, Clang ≥ 10, MSVC ≥ 2019)
- CMake ≥ 3.15
- Git
- Basic understanding of C++ and CMake

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/your-username/PixivTagDownloader-CPP.git
   cd PixivTagDownloader-CPP
   ```

2. **Set up the development environment**
   ```bash
   # Create build directory
   mkdir build && cd build
   
   # Configure with debug symbols
   cmake -DCMAKE_BUILD_TYPE=Debug ..
   
   # Build
   make -j$(nproc)
   ```

3. **Set up configuration**
   ```bash
   # Copy example config
   cp config.yaml.example config.yaml
   # Edit config.yaml with your Pixiv cookie for testing
   ```

## Coding Standards

This project follows the Google C++ Style Guide with some modifications:

### General Guidelines

- **File naming**: Use snake_case for files (e.g., `string_utils.cpp`)
- **Class naming**: Use PascalCase (e.g., `PixivApi`)
- **Function naming**: Use snake_case (e.g., `get_user_info`)
- **Variable naming**: Use snake_case (e.g., `user_id`)
- **Constant naming**: Use UPPER_SNAKE_CASE (e.g., `MAX_RETRIES`)

### Code Style

```cpp
// Header guards: Use #pragma once
#pragma once

// Includes: System headers first, then project headers
#include <string>
#include <vector>
#include "types.h"

// Namespace: All code should be in pixiv namespace
namespace pixiv {

// Class definition
class ExampleClass {
public:
    // Public methods first
    ExampleClass();
    ~ExampleClass();
    
    bool do_something(const std::string& input);
    
private:
    // Private members with trailing underscore
    std::string member_variable_;
    int another_member_;
};

} // namespace pixiv
```

### Documentation

- Use Doxygen-style comments for all public APIs
- Include brief descriptions for all functions
- Document parameters and return values
- Add usage examples for complex functions

```cpp
/**
 * @brief Download artwork from Pixiv
 * @param artwork_id The ID of the artwork to download
 * @param output_path Path where the file should be saved
 * @return true if download successful, false otherwise
 * 
 * Example:
 * @code
 * bool success = download_artwork("12345", "/path/to/output.jpg");
 * @endcode
 */
bool download_artwork(const std::string& artwork_id, const std::string& output_path);
```

### Error Handling

- Use exceptions for exceptional conditions
- Return error codes or optional types for expected failures
- Always provide meaningful error messages
- Log errors appropriately

```cpp
// Good: Clear error handling
std::unique_ptr<UserInfo> get_user_info(const std::string& uid) {
    if (uid.empty()) {
        throw std::invalid_argument("User ID cannot be empty");
    }
    
    auto response = make_api_request("/users/" + uid);
    if (!response.success) {
        // Log error and return nullptr for expected failures
        spdlog::warn("Failed to get user info for UID {}: {}", uid, response.error);
        return nullptr;
    }
    
    return parse_user_info(response.body);
}
```

## Submitting Changes

### Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding standards
   - Add appropriate documentation
   - Update relevant documentation files

3. **Test your changes**
   ```bash
   # Build and test
   cd build
   make -j$(nproc)
   
   # Run basic functionality tests
   ./PixivTagDownloader --help
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "Add feature: brief description
   
   Detailed description of what this commit does.
   Include any breaking changes or important notes."
   ```

5. **Push and create pull request**
   ```bash
   git push origin feature/your-feature-name
   # Create pull request on GitHub
   ```

### Pull Request Guidelines

- **Title**: Use a clear, descriptive title
- **Description**: Explain what changes you made and why
- **Testing**: Describe how you tested your changes
- **Documentation**: Update documentation if needed
- **Breaking changes**: Clearly mark any breaking changes

### Commit Message Format

```
type: brief description (50 chars or less)

Detailed explanation of the change (wrap at 72 chars).
Include motivation for the change and contrast with
previous behavior.

- List any breaking changes
- Reference issues: Fixes #123, Closes #456
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

## Reporting Issues

### Bug Reports

When reporting bugs, please include:

1. **Environment information**
   - Operating system and version
   - Compiler and version
   - CMake version
   - PixivTagDownloader version

2. **Steps to reproduce**
   - Exact commands used
   - Configuration file (remove sensitive data)
   - Expected vs actual behavior

3. **Logs and error messages**
   - Complete error messages
   - Relevant log output
   - Stack traces if available

### Issue Template

```markdown
**Environment:**
- OS: [e.g., Ubuntu 20.04, Windows 10, macOS 12]
- Compiler: [e.g., GCC 9.4, Clang 12, MSVC 2019]
- CMake: [e.g., 3.18.4]
- Version: [e.g., 1.0.0]

**Description:**
Brief description of the issue.

**Steps to Reproduce:**
1. Step one
2. Step two
3. Step three

**Expected Behavior:**
What you expected to happen.

**Actual Behavior:**
What actually happened.

**Logs/Error Messages:**
```
Paste error messages or logs here
```

**Additional Context:**
Any other relevant information.
```

## Feature Requests

We welcome feature requests! Please:

1. **Check existing issues** to avoid duplicates
2. **Describe the use case** - why is this feature needed?
3. **Propose a solution** - how should it work?
4. **Consider alternatives** - are there other ways to achieve this?

### Feature Request Template

```markdown
**Is your feature request related to a problem?**
A clear description of the problem you're trying to solve.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
Other solutions or features you've considered.

**Additional context**
Any other context, screenshots, or examples.
```

## Development Guidelines

### Module Structure

The project is organized into modules:

- `auth/` - Authentication and cookie management
- `api/` - Pixiv API interaction
- `downloader/` - Download management and strategies
- `storage/` - File organization and metadata
- `config/` - Configuration management
- `cli/` - Command line interface
- `core_logic/` - Main application logic
- `utils/` - Utility functions

### Adding New Features

1. **Design first** - Consider the API and how it fits with existing code
2. **Start with headers** - Define interfaces before implementation
3. **Implement incrementally** - Build and test as you go
4. **Update documentation** - Keep docs in sync with code

### Performance Considerations

- Use appropriate data structures
- Avoid unnecessary copying (use references and move semantics)
- Consider memory usage for large downloads
- Profile performance-critical code

### Security Considerations

- Never log or expose sensitive data (cookies, passwords)
- Validate all user inputs
- Use secure HTTP connections
- Handle authentication failures gracefully

## Questions?

If you have questions about contributing:

- Check existing issues and documentation
- Ask in GitHub Discussions
- Email: [<EMAIL>](mailto:<EMAIL>)

Thank you for contributing to PixivTagDownloader!
