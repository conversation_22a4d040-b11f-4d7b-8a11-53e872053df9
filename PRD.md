- ## 项目概述

    本项目旨在开发一个名为 **PixivTagDownloader** 的 **C++** 应用程序，用于根据用户指定的 Pixiv 用户 ID (UID) 下载其作品（包括图片、插画、漫画、小说）。程序通过读取配置文件（`config.yaml`）中的 Cookie 信息进行登录，并提供交互式命令行或命令行参数两种方式，让用户指定要下载的作品标签及其他选项。下载过程将采用多线程或异步机制以提高效率，并加入可配置的随机延迟以避免触发 Pixiv 的访问频率限制。程序在获取作品信息的同时即可启动下载任务，避免串行等待，从而提升整体吞吐量。下载的内容将按照用户高度可定制的目录结构和文件命名规则进行组织和保存。图片和漫画类作品需在保存时生成同名且包含元数据的 TXT 文件；小说类则单独保存为 TXT 文件，内含元数据及正文内容。

    程序名称及可执行文件名称统一为 `PixivTagDownloader`，作者信息：Mannix Sun，联系邮箱：[<EMAIL>](mailto:<EMAIL>)。

    ---

    ## 术语定义

    * **UID**：Pixiv 用户的唯一标识符（User ID）。
    * **PID**：Pixiv 作品的唯一标识符（包括图片/插画/漫画/小说）。
    * **Cookie**：用于身份验证和会话管理的文本数据片段，本项目从配置文件中读取。
    * **Tag**：用户为作品添加的标签，用于分类和搜索。
    * **元数据 (Metadata)**：描述作品信息的数据，例如标题、标签、描述、系列信息、作者、上传日期等。
    * **模块化 (Modularity)**：将程序拆分为多个功能单元，各单元职责单一、可复用。
    * **高内聚 (High Cohesion)**：模块内部各元素协同完成相对独立的功能。
    * **低耦合 (Low Coupling)**：模块之间尽量减少依赖，一个模块的修改对其他模块影响微弱。
    * **并发（Concurrency）/多线程**：利用 C++ 标准线程库或 Boost.Asio 等库实现同时处理多个任务，包括信息获取与下载任务并行。
    * **配置文件**：基于 YAML 格式（`config.yaml`），保存登录 Cookie、下载策略、目录模板等所有可配置项。

    ---

    ## 功能需求

    ### 1. 用户认证与 Cookie 管理

    1. **读取配置文件**

       * 程序启动时，从指定路径（默认为当前目录，也可通过命令行参数或环境变量指定）加载一个名为 `config.yaml` 的配置文件。
       * 配置文件包含 HTTP Cookie 字符串（标准格式：`key1=value1; key2=value2; ...`），以及其他可配置项（如下载并发数、延迟范围、User-Agent 等）。

    2. **Cookie 格式与有效性校验**

       * 支持标准 HTTP Cookie 格式（以 “;” 分隔键值对）。程序需兼容 Cookie 字符串中分号后是否有空格的差异。
       * 读取 Cookie 后，程序通过调用 Pixiv 的需要登录才能访问的 API 端点（例如用户个人主页接口）验证 Cookie 是否有效。
       * 如果配置文件不存在、格式不正确或 Cookie 已失效，程序需打印清晰的错误提示，并允许用户通过命令行参数或重新编辑配置文件的方式指定新的 Cookie，或者安全退出。

    ### 2. 用户交互方式

    1. **启动模式**

       * **交互式模式**：直接运行可执行文件，程序以交互式命令行界面引导用户完成以下步骤：

         1. 输入目标 Pixiv 用户的 UID（仅数字）。
         2. 程序获取目标用户基本信息并提取其所有作品的唯一 Tag 列表。
         3. 询问用户是手动输入需要下载的 Tag（支持多个 Tag，逗号分隔），还是在提取到的 Tag 列表中进行选择。
         4. 如果选择从列表中选择，程序需要分页展示所有 Tag 并支持关键字搜索；用户可以通过上下方向键或数字编号选取一个或多个 Tag。
         5. 确认 Tag 过滤逻辑：

            * **AND**：作品需包含所有选定 Tag。
            * **OR**：作品只需包含任意一个选定 Tag。
            * **NOT (可选增强)**：排除包含指定 Tag 的作品。
         6. 选择要下载的作品类型（可多选：`Illust`、`Manga`、`Novel`，或选择 `all`）。
         7. 确认下载任务，开始执行并实时显示进度（已下载作品数 / 总作品数、当前文件下载进度条）。

       * **命令行参数模式**：
         用户可在启动时通过参数一次性指定所有必要信息，程序直接执行任务，无需交互。必选或常用参数包括：

         * `-u, --uid <UID>`：目标 Pixiv 用户 ID（必需）。
         * `-t, --tags <TAG1,TAG2,...>`：要下载的 Tag 列表，多个 Tag 用逗号分隔。
         * `-l, --logic <and|or>`：Tag 过滤逻辑，默认 `or`。
         * `--all`：若指定此选项，则下载目标用户的所有作品，不做 Tag 筛选。
         * `-T, --type <Illust,Manga,Novel,all>`：下载作品类型，大小写不敏感，多个类型用逗号分隔，默认为 `all`。
         * `-c, --config <PATH>`：配置文件 (`config.yaml`) 路径，若不指定则在当前目录搜索。
         * `--output-dir <PATH>`：输出根目录，默认为程序运行目录下 `Output`。
         * `--download-method <direct|aria2c|aria2rpc>`：选择下载方式（默认 `direct`）。
         * `--aria2rpc-url <URL>`：若使用 aria2 RPC 下载，指定 RPC 服务地址，如 `ws://localhost:6800/jsonrpc`。
         * `--aria2rpc-secret <TOKEN>`：aria2 RPC 授权密钥（可选）。
         * `--threads <NUM>` 或 `--concurrency <NUM>`：并发下载任务数，默认与 CPU 核心数相同。
         * `--delay <MIN-MAX>`：随机延迟范围（秒），如 `1-3`，默认 `1-3`。
         * `--skip-existing|--overwrite-existing|--rename-existing`：文件冲突处理策略。
         * `--log-level <trace|debug|info|warn|error>`：日志级别，默认 `info`。

    2. **进度与日志**

       * 在下载过程中，交互式模式下需在命令行实时展示进度条（已完成作品数/总作品数、当前文件下载进度百分比）。
       * 在所有模式下，程序需将详细日志输出到控制台和/或文件，日志格式包含时间戳、级别、模块、消息。用户可以在配置文件或命令行中配置日志文件路径及日志级别。

    ### 3. 数据获取与筛选

    1. **获取用户基本信息**

       * 根据用户输入的 UID，通过 Pixiv API 或网页解析（依赖 HTTP 请求库和 HTML 解析库）获取目标用户的用户名 (Username)，用于后续目录和文件命名。对用户名进行文件名安全处理（移除非法字符）。

    2. **获取作品列表及元数据**

       * 获取目标 UID 下所有公开作品的列表，包含每个作品的：

         * **PID**（作品 ID）
         * **类型**（`Illust`、`Manga`、`Novel`）
         * **标题**（需 UTF-8 处理）
         * **标签列表**（Tag）
         * **系列信息**（若存在，包括系列标题和系列 ID）
         * **上传日期**及时间（ISO 8601 格式或可配置格式）
         * **图片 URL**（对于图片/插画/漫画；若为多页漫画则需获取所有页面的 URL 列表）
         * **小说内容**（对于小说）
         * **点赞数 (Like\_Count)**、**收藏数 (Bookmark\_Count)**
         * **R18 标志**（True/False）

    3. **Tag 提取与过滤**

       * 从获取到的作品元数据中提取所有唯一 Tag，生成一个 Tag 列表。
       * 根据用户在交互或参数模式下指定的 Tag 列表与过滤逻辑（AND/OR/NOT），对作品进行筛选，确定最终下载列表。

    ### 4. 下载功能与并行策略

    1. **并发获取与下载**

       * 程序采用生产者-消费者模型：

         * **生产者线程/任务**：负责向 Pixiv 请求作品元数据（作品列表和详细信息），将可下载项（包含 URL、文件名、路径等信息）放入任务队列。
         * **消费者线程/任务**：从任务队列中获取下载项并立即开始下载，而无需等待所有作品信息获取完毕。
       * 任务队列可基于线程安全队列（如 C++ 标准库 `std::queue` + `std::mutex` 或基于 Lock-free 方案），确保获取和下载能并行进行。
       * 并发任务数量可在配置文件或命令行中配置，默认值为 CPU 核心数。

    2. **错误处理与重试机制**

       * 对网络请求（包括获取元数据、下载文件）中可能出现的错误（如超时、连接失败、HTTP 错误码）进行捕获和处理。失败时可依据配置进行重试（重试次数和间隔可配置）。
       * 对无法成功下载的作品记录错误日志并跳过，不中断整体任务。

    3. **HTTP 头与反爬虫对策**

       * 在所有 HTTP 请求中设置默认 User-Agent、Referer 等常用头部，且允许用户在配置文件中自定义这些头部字段。
       * 如果目标作品含 R18 内容，需确保 Cookie 对应账号具有浏览权限，否则返回相应错误并跳过。

    4. **下载方式支持**

       * **直接下载 (Direct)**：使用内置 HTTP 客户端库（如 libcurl）下载作品。
       * **Aria2c 命令行 (aria2c)**：将所有待下载 URL 写入一个临时文件，再通过执行外部命令 `aria2c --input-file=<FILE> [其他参数]` 进行批量下载。
       * **Aria2 RPC (aria2rpc)**：通过 JSON-RPC 或 WebSocket Secure (WSS) 与正在运行的 Aria2 服务通信，将下载任务以 RPC 方式提交。

         * 支持可选的证书验证：信任系统 CA、指定自定义 CA 证书、或跳过验证（当跳过验证时需输出安全警告）。
       * 对于 Aria2 节点连接失败或 RPC 授权失败需报错并允许降级到直接下载或命令行方式。

    5. **断点续传**

       * 对直接下载方式启用 HTTP Range 断点续传功能，当本地已有部分文件时可继续下载剩余部分。
       * 对 Aria2 方式默认支持其自身的断点续传机制；程序仅需确保 RPC/命令行参数中传递正确的 `--continue` 或等价选项。

    ### 5. 文件存储与组织

    1. **根目录与可配置性**

       * 默认将所有下载内容保存在程序运行目录下名为 `Output` 的文件夹中。用户可通过配置文件 `config.yaml` 或命令行参数 `--output-dir` 自定义输出根目录。

    2. **目录结构与命名模板**

       * 允许用户在配置文件中通过变量组合定义目录结构和文件命名模板，支持以下变量：

         * `{uid}`：Pixiv 用户 ID。
         * `{username}`：Pixiv 用户名（已做文件名安全处理）。
         * `{pid}`：作品 ID。
         * `{title}`：作品标题（已做文件名安全处理）。
         * `{type}`：作品类型，`Illust`、`Manga` 或 `Novel`。
         * `{page_index}`：多页漫画/插画的页码索引（如 `p0`, `p01` 等，格式可在配置里指定）。
         * `{page_count}`：作品总页数。
         * `{series_title}`：系列标题（若存在，已做文件名安全处理）。
         * `{series_id}`：系列 ID（若存在）。
         * `{upload_date}`：作品上传日期，可指定格式（如 `YYYYMMDD` 或 `%Y*%m*%d` 等）。
         * `{tags}`：作品标签，以指定分隔符连接（如 `{tags:_}` 或 `{tags:#}`），并进行文件名安全处理。
         * `{r18}`：是否为 R18 作品，输出 `R18` 或空字符串。
         * `{like_count}`：点赞数。
         * `{bookmark_count}`：收藏数。
         * `{ext}`：文件原始扩展名（如 `.jpg`、`.png`）。
       * 如果用户未在配置文件中自定义路径模板，默认目录结构为：

         ```
         {output_root}/  
           {uid}_{username}/  
             Images/        # 单页图片与多页插画  
             Manga/         # 漫画  
             Novel/         # 小说  
         ```

         * 在 `Images`、`Manga` 下，根据作品是否属于系列再创建子目录：

           * 属于系列：以系列名称命名的子目录。
           * 不属于系列：使用统一名称 “No\_Series” 作为子目录。

    3. **图片/插画/漫画存储逻辑**

       * **单张图片作品**：直接按用户配置的命名模板保存到对应目录，文件名支持 `{upload_date}_{pid}_p0_{title}{ext}` 格式，其中索引号固定为 `p0`。非法字符需被替换或移除。
       * **多页作品 (同一 PID)**：

         1. 在对应目录下创建一个以 PID 或按模板生成的文件夹。
         2. 将每页图片按 `{upload_date}_{pid}_p{page_index}_{title}{ext}` 命名并保存，其中 `{page_index}` 可根据页码自动补零（如 `p0`, `p1`，或两位数 `p00`, `p01`，格式由配置决定）。
       * **元数据 TXT 文件**：

         * 对于每个 PID，在最终保存位置（单张图片对应目录或多张图片 PID 子目录）创建一个同名或指定前缀的 `.txt` 文件，内容包括：

           ```txt
           Title: [作品标题]  
           Author_UID: [作者UID]  
           Author_Username: [作者用户名]  
           Artwork_PID: [作品PID]  
           Artwork_Type: [Illust 或 Manga]  
           Tags: [tag1, tag2, tag3]  
           Description:  
           [作品描述文本，保留原始换行]  
           Series_Title: [系列标题，如有]  
           Series_ID: [系列ID，如有]  
           Upload_Date: [上传日期及时间，ISO 8601格式或配置格式]  
           Page_Count: [总页数]  
           R18: [True/False]  
           Like_Count: [点赞数]  
           Bookmark_Count: [收藏数]  
           Original_URLs:  
           [每行一个图片 URL]  
           Download_Time: [本次下载完成时间，ISO 8601格式]  
           ```
         * 文件编码统一为 UTF-8。

    4. **小说存储逻辑**

       * **文件保存位置**：根据配置模板生成路径，例如：

         ```
         {output_root}/{uid}_{username}/  
           Novel/  
             {series_folder或No_Series}/  
               {upload_date}_{pid}_{title}.txt  
         ```
       * **文件命名**：支持用户在配置中自定义小说文件名模板，默认 `{upload_date}_{pid}_{title}.txt`，非法字符需被移除或替换。
       * **TXT 文件内容**：

         ```txt
         Title: [小说标题]  
         Author_UID: [作者UID]  
         Author_Username: [作者用户名]  
         Novel_PID: [小说PID]  
         Novel_Type: Novel  
         Upload_Date: [上传时间，ISO 8601格式或配置格式]  
         Tags: [tag1, tag2, tag3]  
         Series_Title: [系列标题，如有]  
         Series_ID: [系列ID，如有]  
         R18: [True/False]  
         Like_Count: [点赞数]  
         Bookmark_Count: [收藏数]  
         Word_Count: [字数]  
         Description:  
         [小说描述，保retain原始换行]  
         Download_Time: [下载完成时间，ISO 8601格式]  
         *** Content ***   # 分隔符可配置  
         [小说正文内容，保retain原始换行与格式]  
         ```
       * 文件编码同样为 UTF-8。

    5. **冲突与路径合法性处理**

       * 在创建目录与文件之前，程序需检查是否已存在同名文件或目录。若发生冲突，根据用户在配置或命令行中指定的策略进行：跳过已有文件、覆盖已有文件或重命名（如在文件名后添加序号或哈希）。
       * 对于 Windows、Linux、macOS 等不同系统的非法文件名字符（如 `<>:\"/\|?*` 等），进行统一替换或移除。
       * 对于过长路径或文件名，若超过系统限制，通过截断或局部哈希替换以确保唯一性，并在日志中进行提示。

    ---

    ## 非功能需求

    ### 1. 架构与模块设计

    * **模块划分**：项目源代码应分为以下逻辑模块，每个模块责任单一、耦合度低：

      * `auth`：负责读取配置文件中的 Cookie 信息并完成 Pixiv 登录验证。
      * `api`：封装与 Pixiv 相关的 HTTP 请求、API 调用和响应解析逻辑。
      * `downloader`：统一调度下载任务，采用生产者-消费者模型并行处理获取与下载任务，支持三种下载方式（Direct / aria2c / aria2rpc），实现并发逻辑、延迟与重试机制。
      * `storage`：根据用户配置的模板生成目录与文件名，处理文件系统操作（创建目录、检查冲突、保存文件与元数据）。
      * `config`：解析并加载 `config.yaml`，提供全局配置访问接口；支持命令行参数覆盖。
      * `cli`：命令行参数解析与交互式界面逻辑。
      * `core_logic`：将认证、数据获取、筛选、下载、存储等流程串联，并提供程序入口。
      * `utils`：通用工具函数，如文件名安全处理、时间格式转换、日志初始化等。

    * **接口与依赖**：

      * 各模块之间使用干净的 C++ 接口（纯虚类+实现或函数 API）进行交互；减少头文件包含，仅暴露必要的数据结构与配置类。
      * 全局依赖管理宜采用 CMake 构建系统，采用 `FetchContent` 或 `find_package` 引入第三方库。

    ### 2. 技术选型与性能

    * **HTTP 客户端**：

      * 使用 **libcurl**（synchronous/异步模式）或 **cpp-httplib**（单头文件、轻量）发起 HTTP/HTTPS 请求。
      * 支持 HTTPS 证书验证和忽略验证两种模式（供连接 aria2 RPC 时使用）。
    * **并发与异步**：

      * 采用生产者-消费者模型结合 C++17/20 标准库的 `std::thread`、`std::mutex`、`std::condition_variable` 实现线程安全队列。
      * 如需更灵活的异步 I/O，可使用 **Boost.Asio** 实现非阻塞网络请求与定时化定时器。
    * **序列化与配置解析**：

      * **nlohmann/json** 用于处理 JSON 格式数据。
      * **yaml-cpp** 用于加载和解析 `config.yaml`。
    * **命令行参数解析与交互式界面**：

      * 使用 **CLI11** 或 **cxxopts** 解析命令行参数。
      * 如果实现交互式多选列表，可采用基于 **ncurses** 的简易界面库，或仅通过文本提示与编号选择。
    * **进度条与用户提示**：

      * 进度条可基于简单的控制台输出自行实现或采纳 **tqdm-cpp** 库。
    * **日志系统**：

      * 采用 **spdlog** 进行分级别日志管理，支持异步日志、日志轮转等功能。
      * 日志格式包含时间戳（ISO 8601）、日志级别、模块名和具体内容。
    * **HTML 解析**（如 Pixiv API 不稳定时改为网页解析）：

      * 可使用 **pugixml** 或 **Gumbo-parser** 进行 HTML 解析。
    * **WebSocket / JSON-RPC 客户端**：

      * 与 aria2 RPC 通信可选用 **WebSocket++**、**Boost.Beast** 或 **ixwebsocket** 实现 WSS 连接。
      * JSON-RPC 请求使用 nlohmann/json 构造/解析。
    * **构建与发布**：

      * 采用通用的 CMake 构建流程，确保在各主要平台（Windows、Linux、macOS）上可编译。
      * 为以下三种 Linux 发行版提供编译脚本示例，支持交叉编译至 X86 Windows、X86 Linux、X86_64 Windows、X86_64 Linux、ARM Linux：

        1. **Ubuntu 系列**：提供脚本用于本地 X86_64 Linux 构建，以及交叉编译至 X86 Windows、X86 Linux、X86_64 Windows 和 ARM Linux。用户只需根据脚本结构准备相应的工具链文件，即可一键执行构建流程。
        2. **Fedora 系列**：类似于 Ubuntu 的脚本结构，说明如何在 Fedora 环境下使用 CMake 工具链文件完成本地和交叉编译任务。
        3. **ArchLinux**：提供适配 ArchLinux 包管理的脚本框架，同样支持本地 X86_64 Linux 构建，以及交叉编译至 X86 Windows、X86 Linux、X86_64 Windows 和 ARM Linux。
      * 上述脚本均封装了生成构建目录、调用 CMake、指定工具链文件以及触发编译的步骤，帮助用户在不同发行版环境中方便地完成多目标平台编译。
      * 最终产物可通过通用打包工具（如 CPack）生成常见安装包格式，便于分发。

    ---

    ## 输入与输出

    ### 输入

    * **配置文件**：`config.yaml`（包含 Cookie、下载配置、目录/文件模板、日志配置等）。
    * **命令行参数**（可选）：覆盖或补充配置文件中相应字段。
    * **交互式输入**（仅在交互模式时）：

      * Pixiv 用户 UID（数字）。
      * 是否手动输入 Tag 或从列表中选择。
      * 选择 Tag 过滤逻辑（AND/OR/NOT）。
      * 选择要下载的作品类型。
      * 确认及执行下载。

    ### 输出

    * **下载的媒体文件**：图片/插画/漫画（JPG、PNG、GIF 等）。
    * **元数据 TXT 文件**：

      * 对于图片/插画/漫画，每个 PID 目录下生成一个同名 `.txt` 文件，内容包括作品所有元信息（见上节 “图片/插画/漫画存储逻辑”）。
      * 对于小说，直接生成包含元信息与正文的 `.txt` 文件（见上节 “小说存储逻辑”）。
    * **目录结构**：根据用户配置或默认模板，将文件组织在 `{output_root}/{uid}_{username}/...` 目录下，并按照类型及系列信息细分子目录。
    * **日志与进度**：

      * 实时在命令行显示下载进度信息。
      * 将详细日志写入指定的日志文件（若配置），包括每个网络请求、关键错误、重试记录等。

    ---

    ## 约束与假设

    1. **运行环境**

       * 用户机器需安装 C++17/20 编译器（如 GCC ≥ 9、Clang ≥ 10、MSVC ≥ 2019）和相应标准库。
       * 构建环境需安装 CMake (≥3.15)、下载所需第三方库（可通过系统包管理器或子模块/FetchContent）。
       * 最终用户只需可执行文件，无需安装开发环境。

    2. **外部依赖**

       * HTTP 客户端库（libcurl 或 cpp-httplib）。
       * JSON 序列化库（nlohmann/json）。
       * YAML 解析库（yaml-cpp）。
       * 日志库（spdlog）。
       * 命令行解析库（CLI11 或 cxxopts）。
       * 并发支持库（C++ 标准库或 Boost）。
       * 可选的 HTML 解析库（pugixml、Gumbo-parser）。
       * Aria2 RPC 通信库（WebSocket++、Boost.Beast 或 ixwebsocket）。

    3. **网络与 Pixiv 限制**

       * 需稳定网络连接以访问 Pixiv。若网络中断或 Pixiv 服务器响应缓慢，程序需能适当重试并记录失败。
       * 依赖 Pixiv 网站当前结构和（非官方）API，若 Pixiv 更新，程序可能需要维护。

    4. **Cookie 前提**

       * 假设用户在配置文件中提供的 Cookie 是有效且未过期的，并且具有访问目标作品权限（包括可能的 R18 内容）。程序不负责获取或生成 Cookie。

    5. **频率限制与反爬虫**

       * 随机延迟与并发数量需平衡效率与被封禁风险，程序不能保证 100% 不触发 Pixiv 的访问限制。

    6. **Aria2 环境**

       * 若用户选择 Aria2 下载方式，需要本地或远程已有正确安装并配置的 Aria2 服务（命令行或 RPC）。
       * 当使用 WSS 连接时，需确保 Aria2 已正确配置证书，否则可选跳过验证但需有安全提示。

    7. **跨平台**

       * 程序需兼容 Windows、Linux、macOS 等主要操作系统，尤其是文件路径处理及线程管理。

    ---

    ## 文档与交付物要求

    1. **用户文档（User Documentation）**

       * **README.md**：项目简介、主要功能、编译与安装指南、快速上手示例、许可证信息。
       * **用户手册**：

         * 详细使用说明（交互模式与命令行模式）。
         * 所有命令行参数及配置项说明。
         * 配置文件 (`config.yaml`) 格式详解及示例。
         * 目录与文件名模板变量说明及示例。
         * Aria2 集成与远程 RPC 配置指南。
         * 常见问题 (FAQ) 与故障排除。
         * 使用条款与免责声明。

    2. **开发者文档（Developer Documentation）**

       * **Doxygen 注释**：源代码应添加完善的注释，方便生成 HTML API 文档。
       * **架构说明**：介绍各模块职责及相互调用关系。
       * **构建指南**：如何在不同平台上通过 CMake 构建项目。

    3. **贡献指南（CONTRIBUTING.md）**

       * 报告问题、提交功能需求或 BUG 的流程。
       * 代码风格规范（遵循 Google C++ Style Guide）。
       * 新功能应包含相应说明，但无需实际实现单元测试。

    4. **示例文档与片段（Examples）**

       * 提供常见使用场景的完整命令行示例及对应配置文件片段。
       * 包含“下载单个 UID 的指定 Tag”、“下载所有作品并通过 Aria2 RPC 下载”、“断点续传示例”等示例。

    5. **语言支持**

       * 文档需提供**中文（简体）**、**日文**与**英文**三个版本，方便国内外用户阅读。